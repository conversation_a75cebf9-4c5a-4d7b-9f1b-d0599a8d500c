<template>
  <div :class="{ outer: tail.position === 'outer' }">
    <a-button
      type="text"
      size="mini"
      style="margin-left: 8px; padding: 0 8px"
      @click="selectCtx.handleReverse"
      v-if="tail.type === 'reverse'"
      >反选
    </a-button>
    <KolaButton :config="computedDButtonProps" v-if="tail.type === 'DButton'" />
    <div v-if="tail.type === 'DButtonGroup'">
      <a-space>
        <KolaButton
          v-for="(item, indx) in tail.group"
          :key="indx"
          :config="item"
        />
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { inject, computed, Ref } from 'vue';
  import { isFunction } from 'lodash';
  import { SelectTail } from '../../../../types/form';
  import { SelectContext, selectInjectionKey } from '../context';
  import KolaButton from '../../../../button/index.vue';

  const props = defineProps<{
    tail: SelectTail;
    path?: string;
  }>();

  const selectCtx = inject<Partial<SelectContext>>(selectInjectionKey, {});
  const formData = inject<Ref<FormData>>('formData');

  const computedDButtonProps = computed(() => {
    if (isFunction(props.tail.props)) {
      return props.tail.props(selectCtx.getSourceData, formData, props.path);
    }
    return props.tail.props || {};
  });
</script>

<style scoped lang="less">
  .outer {
    position: absolute;
    left: 100%;
    margin-left: 4px;
    top: 50%;
    transform: translateY(-50%);
  }
</style>
