<script setup lang="ts">
  import {
    ref,
    computed,
    nextTick,
    watchEffect,
    onMounted,
    onBeforeUnmount,
    defineOptions,
    defineExpose,
  } from 'vue';

  defineOptions({ name: 'TextOverflowTooltip' });

  interface Props {
    text: string; // 已过滤的 HTML 字符串
    lines?: number; // 显示行数，默认 1
    className?: string; // 额外类名
    tooltipPosition?:
      | 'top'
      | 'tl'
      | 'tr'
      | 'bottom'
      | 'bl'
      | 'br'
      | 'left'
      | 'lt'
      | 'lb'
      | 'right'
      | 'rt'
      | 'rb';
  }

  const props = withDefaults(defineProps<Props>(), {
    lines: 1,
    className: '',
    tooltipPosition: 'top',
  });

  const containerRef = ref<HTMLElement | null>(null);
  const showTooltip = ref(false);

  /* ----------------------------------------------------------
   * 计算 class / style
   * -------------------------------------------------------- */
  const baseClass = computed(() =>
    props.lines === 1 ? 'single-line-ellipsis' : 'multi-line-ellipsis'
  );

  /* ----------------------------------------------------------
   * 溢出检测
   * -------------------------------------------------------- */
  async function calcOverflow() {
    await nextTick();
    if (!containerRef.value || !props.text) {
      showTooltip.value = false;
      return;
    }

    const el = containerRef.value;
    showTooltip.value =
      props.lines === 1
        ? el.scrollWidth > el.clientWidth
        : el.scrollHeight > el.clientHeight;
  }

  /* ResizeObserver 监听尺寸变化 */
  let ro: ResizeObserver | null = null;

  onMounted(() => {
    ro = new ResizeObserver(calcOverflow);
    containerRef.value && ro.observe(containerRef.value);
    calcOverflow();
  });

  onBeforeUnmount(() => {
    ro?.disconnect();
    ro = null;
  });

  /* props / DOM 变化自动 recalculation */
  watchEffect(calcOverflow);

  /* 手动暴露方法（可选） */
  defineExpose({ reflow: calcOverflow });
</script>

<template>
  <div :class="[baseClass, props.className]" ref="containerRef">
    <a-tooltip
      v-if="showTooltip"
      :content="props.text"
      :position="props.tooltipPosition"
    >
      <!-- 使用 v-html：为了兼容搜索的高亮显示 -->
      <!-- eslint-disable-next-line vue/no-v-html -->
      <span v-html="props.text" />
    </a-tooltip>

    <!-- eslint-disable-next-line vue/no-v-html -->
    <span v-else v-html="props.text" />
  </div>
</template>

<style scoped lang="less">
  /* 单行 */
  .single-line-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 多行 */
  .multi-line-ellipsis {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: v-bind('props.lines');
    line-clamp: v-bind('props.lines');
  }
</style>
