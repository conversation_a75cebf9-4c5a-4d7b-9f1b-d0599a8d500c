<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="page-tabs">
    <a-tabs
      :active-key="activePageKey"
      lazy-load
      @change="handlePageChange"
      justify
    >
      <template #extra>
        <a-popover v-if="!!$slots.tooltip" position="left">
          <template #content>
            <slot name="tooltip"></slot>
          </template>
          <icon-exclamation-circle class="tooltip-icon" size="22" />
        </a-popover>
      </template>
      <a-tab-pane v-for="page in pages" :key="page.key">
        <template #title>
          {{ page.title }}
          <a-popover
            v-if="!!page.tooltip"
            :content="page.tooltip"
            position="bottom"
          >
            <template #content>
              <div v-html="page.tooltip"></div>
            </template>
            <icon-question-circle />
          </a-popover>
        </template>
        <component :is="page.component"></component>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import useURLQuery from '@/hooks/url-query';

  defineOptions({ name: 'PageTabs' });

  type Page = {
    title: string;
    key: string | number;
    tooltip?: string;
    component: object;
  };

  const props = defineProps<{
    pages: Page[];
  }>();

  defineSlots<{
    tooltip?: () => any;
  }>();

  const activePageKey = ref(props.pages[0].key);

  const { getQuery, setQuery } = useURLQuery();

  const { pageType } = getQuery();
  if (props.pages.some((p) => p.key === pageType)) {
    activePageKey.value = pageType as string;
  }

  const handlePageChange = (key: string | number) => {
    // activePageKey.value = key;
    setQuery({ pageType: `${key}` });
  };
</script>

<style scoped lang="less">
  .page-tabs {
    height: calc(100vh - 60px);

    :deep(.arco-tabs) {
      > .arco-tabs-content {
        padding-top: 0;
      }
    }
  }

  .tooltip-icon {
    margin-right: 20px;
    color: var(--color-neutral-6);
    cursor: pointer;
  }
</style>
