import { DForm, FormData } from './form';

type ModalUI = {
  width?: number | string;
  fullscreen?: boolean;
};

type BaseOperation = {
  action: (data: FormData, customParams?: any) => Promise<any>;
  ui?: ModalUI;
  disabled?: ((record: any) => boolean) | boolean;
};

type Operation = {
  create?: BaseOperation & {
    form: DForm | (() => Promise<DForm>);
  };
  modify?: BaseOperation & {
    form: DForm | (() => Promise<DForm>);
    fetchDetail?: (data: FormData) => Promise<FormData>;
  };
  copy?: BaseOperation & {
    form: DForm;
    fetchDetail?: (data: FormData) => Promise<FormData>;
  };
  remove?: BaseOperation;
  batchRemove?: BaseOperation;
  batchUpload?: BaseOperation & {
    templateName?: string;
    tips?: string;
    file?: {
      size?: number;
      limit?: number;
      type?: 'excelOrCsv';
    };
  };
  batchButtons?: {
    type?: string;
    icon?: string;
    label: string;
    onClick: (data: any) => void;
  }[];
  customTableOperation?: {
    [key: string]: {
      title: string;
      component: any;
      ui?: ModalUI;
      needFooter?: boolean;
      disabled?: (record: any) => boolean;
    };
  };
};

export default Operation;
