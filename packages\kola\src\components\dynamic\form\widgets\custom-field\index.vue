<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
    :rules="field.rules"
    :style="field.style"
  >
    <component
      :is="field.component"
      v-model="value"
      :params="field.params"
      :path="`${path ? `${path}.` : ''}${field.name}`"
    />
    <template #extra v-if="field.extra">
      <component :is="field.extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import { CustomField } from '../../../types/form';

  const props = defineProps<{
    field: CustomField;
    path: string;
  }>();

  const formData = inject('formData');

  const value = defineModel<any>({
    set(val) {
      props.field.onChange?.(val, formData);
      return val;
    },
  });
</script>
