<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
  >
    <a-switch v-model="value" />
  </a-form-item>
</template>

<script setup lang="ts">
  import { BooleanField } from '../../../types/form';

  const value = defineModel<boolean>();

  defineProps<{
    field: BooleanField;
    path: string;
  }>();
</script>
