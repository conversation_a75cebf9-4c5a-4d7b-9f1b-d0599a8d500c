<template>
  <div class="message-item">
    <a-checkbox
      v-if="canChoose"
      class="checkbox"
      v-model="checkedItem"
      @change="handleChange"
    ></a-checkbox>
    <div :class="messageClass">
      <!-- eslint-disable-next-line vue/no-v-html -->
      <div v-html="sanitizedMarkdown"></div>
    </div>
    <div class="time">{{ showTime }}</div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, watchEffect } from 'vue';
  import MarkdownIt from 'markdown-it';
  import hljs from 'highlight.js';
  import DOMPurify from 'dompurify';
  import 'highlight.js/styles/github.css';
  import { formatTimeToDiff } from '../../utils/time';
  import { highlightSearchText } from '../../utils/text';

  const md = new MarkdownIt({
    html: true, // 启用 HTML 支持
    highlight(str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang }).value;
        } catch (__) {
          return '';
        }
      }
      return ''; // use external default escaping
    },
  });

  const emit = defineEmits(['checkAdd', 'checkDelete']);
  const props = defineProps<{
    id: number;
    message: string;
    type: 'my' | 'assistant';
    time: string | undefined;
    canChoose: boolean;
    checkList: any[];
    searchText?: string;
  }>();
  const checkedItem = ref(false);
  const showTime = computed(() => {
    return formatTimeToDiff(props.time);
  });
  const messageClass = computed(() => {
    return props.type === 'my' ? 'message-wrapper my' : 'message-wrapper';
  });
  const compiledMarkdown = ref('');

  watchEffect(() => {
    if (!props.searchText) {
      compiledMarkdown.value = md.render(props.message);
    } else {
      const highlightedMessage = highlightSearchText(
        props.message,
        props.searchText
      );
      compiledMarkdown.value = md.render(highlightedMessage);
    }
  });

  const sanitizedMarkdown = computed(() => {
    return DOMPurify.sanitize(compiledMarkdown.value);
  });

  watch(
    () => props.checkList,
    (value) => {
      if (value) {
        const target = value.find((item) => item === props.id);
        checkedItem.value = !!target;
      }
    },
    { deep: true }
  );

  const handleChange = (check) => {
    if (check) {
      emit('checkAdd', props.id);
    } else {
      emit('checkDelete', props.id);
    }
  };
</script>

<style scoped>
  .message-item {
    width: 100%;
    height: auto;
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .checkbox {
      margin-right: 8px;
    }

    .time {
      width: 68px;
      text-align: center;
      color: rgb(0 0 0 / 52%);
      margin-left: auto;
      font-size: 12px;
    }

    .message-wrapper {
      max-width: 474px;
      padding: 0 13px;
      height: auto;
      background: #fff;
      border-radius: 0 8px 8px;
      border: 1px solid #d8d8d8;
      position: relative;
      text-align: left;
      overflow: hidden;
      word-wrap: break-word;
      overflow-wrap: break-word;

      &.my {
        background: rgb(0 184 33 / 48%);
      }
    }

    :deep(.highlight) {
      color: #2166ff;
    }
  }
</style>
