import { DButton } from './button';
import { DForm } from './form';
import { DTable } from './table';

export type DTableSelector = {
  filter?: DForm | undefined;
  valueKey: string;
  labelKey: string;
  columns: DTable['columns'];
  load?: DTable['load'] | undefined;
  isMultiple?: boolean;
  extraFields?: string[];
  extraValue?: Record<string, any>;
  selectAllFields?: boolean;
  options?: DButton[];
  showFilterOperation?: boolean;
  hideTarget?: boolean;
  showCount?: boolean | ((count: number) => string);
  showAllCount?: boolean;
  showSelectCount?: boolean | ((count: number) => string);
  hideSelection?: boolean;
  showClear?: boolean;
  customTip?: string;
  upToDateTableData?: any;
  customPagination?: any;
  rightOperationStyle?: any;
  autoLoad?: boolean | undefined;
};
