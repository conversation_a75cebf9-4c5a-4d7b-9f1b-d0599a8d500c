import { cloneDeep, get, isArray, isFunction } from 'lodash';
import type { Field, SelectField } from '../types/form';

/**
 * 根据 formData 渲染出明细
 */
export type params = {
  formData: Record<string, any>;
  formConfig: Field[];
  pickPaths: string[];
};

export type detailReturn = {
  label: string;
  value: string;
};

type FattenConfig = Field & { formatter?: (val: any) => string };

export function formToDetail({
  formData,
  formConfig,
  pickPaths,
}: params): detailReturn[] {
  const fattenConfig: FattenConfig[] = [];
  flattenFormDetail(formConfig, '', fattenConfig);

  return pickPaths.map((path) => {
    const pathValue = get(formData, path);
    const pathConfig = fattenConfig.find((v) => v.name === path);

    return {
      label:
        typeof pathConfig?.label === 'function' ? '-' : pathConfig?.label || '',
      value: formValueToName(pathValue, pathConfig),
    };
  });
}

function flattenFormDetail(
  formConfig: Field[],
  originPath: string,
  result: Field[]
): void {
  formConfig.forEach((field: Field) => {
    const path = originPath ? `${originPath}.${field.name}` : field.name;
    if (get(field, 'fields', []).length > 0) {
      const newFields: Field[] = get(field, 'fields', []);
      flattenFormDetail(newFields, path, result);
    } else {
      result.push(cloneDeep({ ...field, name: path }));
    }
  });
}

function formValueToName(value: any, config: FattenConfig | undefined): any {
  if (!config) {
    return '';
  }

  switch (config.type) {
    case 'object': {
      return '';
    }
    case 'text':
    case 'wildcard':
    case 'number': {
      return value;
    }
    case 'select': {
      const source = (config as SelectField).source?.data || [];
      if (typeof source === 'function') {
        return '';
      }
      if (isArray(value)) {
        return value.map((v) => source.find((s) => s.value === v)?.label);
      }
      return source.find((s) => s.value === value)?.label;
    }
    default: {
      if (isFunction(config.formatter)) {
        return config.formatter(value);
      }
      return value;
    }
  }
}
