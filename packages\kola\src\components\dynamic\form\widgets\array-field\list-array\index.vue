<template>
  <div v-for="(item, index) in value" :key="index" class="array-item">
    <WidgetByType
      :field="field.item"
      :path="`${path}[${index}]`"
      v-model="value[index]"
      :hide-label="true"
    />
    <a-button
      v-if="!hideOperation"
      @click="handleAdd(index)"
      class="add-btn"
      :disabled="!canAdd"
    >
      <template #icon>
        <icon-plus />
      </template>
    </a-button>
    <a-button
      @click="handleRemove(index)"
      class="remove-btn"
      v-if="!hideOperation"
      :disabled="!canRemove"
    >
      <template #icon>
        <icon-minus />
      </template>
    </a-button>
  </div>
</template>

<script setup lang="ts">
  import { ArrayField } from '../../../../types/form';
  import WidgetByType from '../../widget-by-type.vue';

  const value = defineModel<any[]>({ default: [] });

  defineProps<{
    field: Array<PERSON>ield;
    path: string;
    canAdd: boolean;
    canRemove: boolean;
    hideOperation?: boolean;
  }>();

  const emits = defineEmits<{
    add: [index: number];
    remove: [index: number];
  }>();

  const handleAdd = (index: number) => {
    emits('add', index);
  };

  const handleRemove = (index: number) => {
    emits('remove', index);
  };
</script>

<style scoped>
  .array-item {
    display: flex;
  }

  .add-btn {
    margin-left: 8px;
  }

  .remove-btn {
    margin-left: 8px;
  }
</style>
