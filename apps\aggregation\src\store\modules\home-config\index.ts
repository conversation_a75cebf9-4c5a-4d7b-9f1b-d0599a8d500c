import { defineStore } from 'pinia';
import { getHomeConfig } from '@/api/home';
import { HomeConfigState } from './types';

const useHomeConfigStore = defineStore('home-config', {
  state: (): HomeConfigState => ({
    access_guide_video: '',
    access_guide_image: '',
    business_contact: {
      email: '',
      phone: '',
      qrcode: '',
    },
    capability_list: [
      {
        app_list: [
          {
            desc: '',
            id: 0,
            logo: '',
            name: '',
            star: 0,
          },
        ],
        id: 0,
        level: 0,
        name: '',
      },
    ],
    case_list: [
      {
        desc: '',
        id: 0,
        image: '',
        name: '',
      },
    ],
    developer_comment_feat: '',
    developer_comment_list: [
      {
        content: '',
        logo: '',
        name: '',
        type: '',
      },
    ],
    partner_list: [
      {
        logo: '',
        name: '',
      },
    ],
    privacy_link: '',
    service_link: '',
    access_link: '',
    hasInit: false,
  }),

  getters: {
    homeConfig(state: HomeConfigState): HomeConfigState {
      return { ...state };
    },
  },

  actions: {
    async getHomeConfig() {
      if (this.homeConfig.hasInit) return;
      this.homeConfig.hasInit = true;
      const res = await getHomeConfig();
      this.$patch({ ...res.data, hasInit: true });
    },
  },
});

export default useHomeConfigStore;
