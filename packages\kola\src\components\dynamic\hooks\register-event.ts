import { inject, onMounted, onUnmounted } from 'vue';
import emitter from '../emitter';
import { PageEvent } from '../types/event';

const useRegisterEvent = (
  event: PageEvent['type'],
  callback,
  pageId?: string
) => {
  pageId = pageId ?? inject<string>('pageId', '');
  const eventName = pageId ? `${pageId}-${event}` : event;

  onMounted(() => {
    emitter.on(eventName, callback);
  });

  onUnmounted(() => {
    emitter.off(eventName, callback);
  });
};

export default useRegisterEvent;
