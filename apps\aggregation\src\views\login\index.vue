<template>
  <div class="login-bg">
    <div class="login-logo">
      <LogoIcon />
    </div>
    <div class="login-card">
      <div class="login-card-left">
        <div class="login-title">联系商务</div>

        <a-space :size="30" style="width: 100%">
          <a-card class="qrcode-card" :bordered="true">
            <img class="qrcode-img" :src="qrcodeUrl" alt="微信二维码" />
            <div class="qrcode-tip">请微信扫码咨询</div>
          </a-card>
          <div class="divider"></div>
        </a-space>
      </div>

      <div class="login-card-right">
        <div class="login-title">快捷登录</div>
        <PhoneForm />
      </div>
    </div>
    <div class="login-footer">
      Copyright © 西安诚优网络科技有限责任公司 版权所有
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import useHomeStore from '@/store/modules/home-config';
  import LogoIcon from '@/assets/images/logo.svg?component';

  import PhoneForm from './form/phone-form.vue';

  const homeConfigStore = useHomeStore();

  const qrcodeUrl = computed(() => {
    return homeConfigStore.homeConfig.business_contact.qrcode;
  });

  onMounted(() => {
    homeConfigStore.getHomeConfig();
  });
</script>

<style scoped lang="less">
  .login-bg {
    min-height: 100vh;
    background: url('@/assets/images/login-bg.png') no-repeat center center;
    background-size: cover;
    position: relative;
    display: flex;
    flex-direction: column;

    :deep(.arco-btn-text:hover) {
      background-color: transparent;
    }
  }

  .login-logo {
    width: 196px;
    height: 26px;
    position: absolute;
    top: 27px;
    left: 32px;
    z-index: 10;
    margin: 0;

    img {
      height: 32px;
    }
  }

  .login-card {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 32px 0 rgb(34 85 179 / 8%);
    margin: 0;
    padding: 100px 162px;
    justify-content: space-between;
    width: 1000px;
  }

  .login-card-left {
    width: 240px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    .login-title {
      font-size: 20px;
      font-weight: 600;
      color: #222;
      margin-bottom: 24px;
      text-align: center;
    }

    .qrcode-card {
      width: 240px;
      text-align: center;
      height: 100%;

      .qrcode-img {
        width: 100%;
        height: 200px;
        margin: 0 auto;
        display: block;
        object-fit: cover;
      }

      .qrcode-tip {
        margin-top: 12px;
        color: #888;
        font-size: 14px;
      }
    }
  }

  .divider {
    width: 1px;
    background: rgb(0 0 0 / 12%);
    height: 260px;
  }

  .login-card-right {
    width: 340px; // 固定宽度，避免被挤压
    max-width: 340px;
    display: flex;
    flex-direction: column;

    .login-title {
      font-size: 20px;
      font-weight: 600;
      color: #222;
      margin-bottom: 24px;
      text-align: left;
    }
  }

  .login-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 32px;
    color: #888;
    font-size: 12px;
    text-align: center;
    margin: 0;
  }
</style>
