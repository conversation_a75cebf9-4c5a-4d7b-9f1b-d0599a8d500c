<template>
  <a-scrollbar
    style="flex: 1; height: 459px; overflow-y: auto"
    :id="`cascader-column-${level}`"
  >
    <ul class="arco-cascader-list arco-cascader-list-strictly">
      <cascader-option
        v-for="item in option"
        :key="item.key"
        :option="item"
        :is-multiple="isMultiple"
        :active="selectedPath.includes(item.key)"
        :check-strictly="false"
        :path-label="false"
        :search-option="false"
        :is-show-value="props.isShowValue"
        :ref="
          (el) => {
            if (el && el.$el) {
              itemRefsMap[item.key] = el.$el;
            }
          }
        "
      ></cascader-option>
    </ul>
  </a-scrollbar>
</template>

<script setup lang="ts">
  import { watch, reactive, onMounted, nextTick } from 'vue';
  import { CascaderOptionInfo } from './interface';
  import CascaderOption from './cascader-option.vue';

  const props = defineProps<{
    option: CascaderOptionInfo[];
    selectedPath: string[];
    activeKey?: string;
    isMultiple: boolean;
    level: number;
    isShowValue?: boolean;
  }>();

  const itemRefsMap = reactive<{ [key: string]: HTMLElement | null }>({});
  const scrollElement = (value: string[]) => {
    if (Array.isArray(value) && value.length > 0) {
      const activeItem = value[props.level];
      const el = itemRefsMap[activeItem] as any;

      const container = document.getElementById(
        `cascader-column-${props.level}`
      ) as any;

      const containerRect = container?.getBoundingClientRect() as any;
      const elRect = el?.getBoundingClientRect() as any;

      // 判断 li 是否在容器的可视区域内
      if (
        elRect?.top >= containerRect?.top &&
        elRect?.bottom <= containerRect?.bottom
      ) {
        return;
      }
      const offset = (el?.offsetTop ?? 0) - (container?.offsetTop ?? 0);
      container.scrollTo({ top: offset, behavior: 'instant' });
    }
  };
  watch(
    () => props.selectedPath,
    (value) => {
      setTimeout(() => {
        scrollElement(value);
      }, 100 * (props.level + 1));
    }
  );
  onMounted(() => {
    setTimeout(() => {
      scrollElement(props.selectedPath);
    }, 100 * (props.level + 1));
  });
</script>
