<template>
  <div class="page-component">
    <div class="home-container flex-col">
      <span class="brand-title">NovaAI</span>
      <span class="brand-desc">AI一站聚合，赋能每一个AI创想</span>
      <div class="home-header flex-row justify-between">
        <div class="market-btn-wrapper flex-col" @click="handleMarket">
          <button class="market-btn">应用市场</button>
        </div>
        <div
          class="contact-wrapper flex-row justify-between align-center"
          @click="handleContact"
        >
          <button class="contact-btn">联系商务</button>
          <icon-arrow-right />
        </div>
      </div>

      <Function :capability-list="homeConfig.capability_list" />

      <span class="section-title">快速接入，简单方便</span>
      <div class="feature-box">
        <video
          :src="homeConfig.access_guide_video"
          class="feature-bg"
          autoplay
          muted
          loop
        >
        </video>
      </div>

      <Case :case-list="homeConfig.case_list" />
      <ClientLogos :partner-list="homeConfig.partner_list" />
      <DevTestimonials
        :quotes="homeConfig.developer_comment_feat"
        :developer-comment-list="homeConfig.developer_comment_list"
      />
    </div>
    <div class="integration-section flex-col">
      <span class="integration-title">一键集成，高效开发，智能引擎</span>
      <div class="integration-btn-wrapper flex-col" @click="handleContact">
        <span class="integration-btn">联系商务</span>
      </div>
    </div>
    <a-modal
      v-model:visible="businessVisible"
      :footer="false"
      width="520px"
      simple
      :closable="true"
    >
      <template #title>联系商务 </template>
      <div class="contact-info">
        <div>联系方式：+86 ***********</div>
        <a-image
          :src="homeConfig.business_contact.qrcode"
          alt="二维码"
          width="207px"
        />
        <span>请微信扫码咨询</span>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import useHomeConfigStore from '@/store/modules/home-config';
  import ClientLogos from './components/ClientLogos.vue';
  import DevTestimonials from './components/DevTestimonials.vue';
  import Function from './components/Function.vue';
  import Case from './components/Case.vue';

  const router = useRouter();

  const homeConfigStore = useHomeConfigStore();
  const homeConfig = computed(() => homeConfigStore.homeConfig);

  const businessVisible = ref(false);
  const handleContact = () => {
    businessVisible.value = true;
  };

  const handleMarket = () => {
    router.push('/app');
  };

  onMounted(() => {
    homeConfigStore.getHomeConfig();
  });
</script>

<style scoped lang="less">
  .home-container {
    position: relative;
    padding: 0 160px;
    width: 100%;
    overflow: hidden;

    .brand-title {
      height: 56px;
      overflow-wrap: break-word;
      color: rgb(255 116 23 / 100%);
      font-size: 40px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 56px;
      margin: 93px 0 0;
    }

    .brand-desc {
      width: 1027px;
      height: 103px;
      overflow-wrap: break-word;
      color: rgb(0 0 0 / 84%);
      font-size: 60px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 103px;
      margin: 16px 0 0;
    }

    .home-header {
      width: 295px;
      height: 54px;
      margin: 30px 0 0;

      .market-btn-wrapper {
        height: 54px;
        width: 164px;
        background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%), #161c2d;
        border-radius: 10px;

        .market-btn {
          width: 80px;
          height: 28px;
          overflow-wrap: break-word;
          color: rgb(255 255 255 / 100%);
          font-size: 20px;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 28px;
          margin: 13px 0 0 42px;
          cursor: pointer;
        }
      }

      .contact-wrapper {
        width: 87px;
        height: 22px;
        margin-top: 16px;
        cursor: pointer;

        .contact-btn {
          width: 64px;
          height: 22px;
          overflow-wrap: break-word;
          color: rgb(0 0 0 / 84%);
          font-size: 16px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 22px;
          cursor: pointer;
        }

        .contact-icon {
          width: 11px;
          height: 9px;
          margin-top: 7px;
        }
      }
    }

    .section-title {
      height: 67px;
      overflow-wrap: break-word;
      color: rgb(0 0 0 / 84%);
      font-size: 32px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 67px;
      margin: 45px auto 0;
    }

    .feature-box {
      // background-color: rgb(0 0 0 / 6.4%);
      // border-radius: 10px;
      height: auto;
      width: 100%;
      margin: 48px 0 0;

      .feature-bg {
        border-radius: 10px;
        width: 70%;
        margin: 0 auto;
        display: block;
      }
    }
  }

  .integration-section {
    width: 100%;
    height: 345px;
    background: #f9fcff;
    margin: 0 auto;

    .integration-title {
      overflow-wrap: break-word;
      color: rgb(0 0 0 / 84%);
      font-size: 40px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 56px;
      margin: 80px auto 0;
    }

    .integration-btn-wrapper {
      width: 145px;
      height: 43px;
      margin: 56px auto 91px;
      background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
      border-radius: 8px;

      .integration-btn {
        width: 80px;
        height: 28px;
        overflow-wrap: break-word;
        color: rgb(255 255 255 / 100%);
        font-size: 20px;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 28px;
        margin: 7px 0 0 32px;
      }
    }
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 16px;
    color: rgb(0 0 0 / 84%);
    line-height: 24px;
    gap: 16px;
  }
</style>
