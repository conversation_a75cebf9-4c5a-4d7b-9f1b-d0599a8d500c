import { computed, nextTick, ref } from 'vue';
import { uniq } from 'lodash';

function useCheckAll(props) {
  const indeterminate = ref(false);
  const checkedAll = ref(false);

  function changeAll({
    flag,
    modelValue,
    sourceData,
    labelKey,
    valueKey,
    inputText,
  }) {
    if (inputText) {
      const selectValue = sourceData.value
        .filter((option) => {
          return option[labelKey.value]
            .toLowerCase()
            .includes(inputText.toLowerCase());
        })
        .map((item) => item[valueKey.value]);
      modelValue.value = uniq([...modelValue.value, ...selectValue]);
      nextTick(() => {
        toggleIndeterminate(modelValue.value, sourceData);
      });
    } else {
      indeterminate.value = false;
      if (flag) {
        checkedAll.value = true;
        modelValue.value = sourceData.value
          .filter((item) => item.disabled !== true)
          .map((item: any) => item[valueKey.value]);
      } else {
        checkedAll.value = false;
        modelValue.value = [];
      }
    }
  }

  function toggleIndeterminate(values, sourceData) {
    if (values.length === sourceData.value.length) {
      checkedAll.value = true;
      indeterminate.value = false;
    } else if (values.length === 0) {
      checkedAll.value = false;
      indeterminate.value = false;
    } else {
      checkedAll.value = false;
      indeterminate.value = true;
    }
  }

  return {
    indeterminate,
    checkedAll,
    changeAll,
    toggleIndeterminate,
  };
}

export default useCheckAll;
