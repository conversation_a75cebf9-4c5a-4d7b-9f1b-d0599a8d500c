import { computed, onUnmounted, ref } from 'vue';

const batchStatus = ref(false);
const selectedKeys = ref([]);
const selectedItems = ref<any[]>([]);
const showBatch = ref(false);
const batchTableData = ref<any[]>([]);
export default function useBatch() {
  // const tableBatchConfig = computed(() => {
  //   if (showBatch.value) {
  //     return {
  //       type: 'checkbox',
  //       showCheckedAll: true,
  //       onlyCurrent: false,
  //     };
  //   }

  //   return null;
  // });

  function changeBatchStatus(status: boolean, double = true) {
    batchStatus.value = status;
    // if (double) {
    //   showBatch.value = status;
    // }
    // 取消的时候清空
    if (!status) {
      selectedKeys.value = [];
      selectedItems.value = [];
    }
  }

  const curBatchStatus = computed(() => {
    return selectedKeys.value.length > 0;
    // return batchStatus.value;
  });

  // onUnmounted(() => {
  //   // changeBatchStatus(false);
  // });

  return {
    batchStatus,
    // showBatch,
    changeBatchStatus,
    selectedKeys,
    curBatchStatus,
    // tableBatchConfig,
    selectedItems,
    batchTableData,
  };
}
