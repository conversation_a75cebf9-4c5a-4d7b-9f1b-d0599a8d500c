import { Component, CSSProperties, Ref } from 'vue';
import { SelectOptionData, CascaderOption } from '@arco-design/web-vue';
import { TimePickerProps } from '@arco-design/web-vue/es/time-picker/interface';
import { DButton } from './button';

type ReactiveParams = [
  Field: Field,
  formData: Record<string, any>,
  path: string,
  keyword?: string
];

export type Option = {
  value: string | number | any[];
  label: string;
  subLabel?: string;
  disabled?: boolean;
  icon?: string;
  tooltip?: string;
  extra?: any;
};

type SelectSource = {
  labelKey?: string;
  valueKey?: string;
  childrenKey?: string;
  data?: Option[] | ((...arg: ReactiveParams) => Promise<Option[]> | Option[]);
  subKey?: string; // 副label
};

export type Rule = {
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  max?: number;
  min?: number;
  pattern?: RegExp;
  message?: string;
  validator?: (value: any, callback: (error?: string) => void) => void;
};

export type BaseField = {
  name: string;
  label: string | ((...arg: ReactiveParams) => string);
  type: string;
  required?: boolean | ((...arg: ReactiveParams) => boolean);
  disabled?: boolean | ((...arg: ReactiveParams) => boolean);
  disabledOn?: (value: any, formData: Record<string, any>) => boolean;
  span?: number;
  startNewRow?: boolean;
  hideLabel?: boolean;
  labelWidth?: string | number;
  tooltip?: string | ((field: any, formData: Record<string, any>) => string);
  rules?: Rule[] | ((...arg: ReactiveParams) => any[]);
  style?: CSSProperties;
  extra?: any;
  innerExtra?: any;
  hideAsterisk?: boolean;
  visibleOn?: (value: any, formData: Record<string, any>) => boolean;
  onChange?: (value: any, formData: any, oldValue?: any, path?: string) => void;
  setter?: (value: any, formData: any, path?: string) => any;
  disabledTooltip?:
    | string
    | ((field: any, formData: Record<string, any>) => string);
};

export type TextField = BaseField & {
  type: 'text';
  placeholder?: ((...arg: ReactiveParams) => string) | string;
  maxLength?: number;
  preText?: string;
  readonly?: boolean;
  allowClear?: boolean;
  showWordLimit?: boolean;
  wordLength?: (value: string) => number;
  wordSlice?: (value: string, maxLength: number) => string;
  onBlur?: ({
    value,
    formData,
  }: {
    value: string;
    formData: Record<string, any>;
  }) => void;
  onClear?: ({
    value,
    formData,
  }: {
    value: string;
    formData: Record<string, any>;
  }) => void;
};
export type TextTagField = BaseField & {
  type: 'textTag';
  placeholder?: ((...arg: ReactiveParams) => string) | string;
  readonly?: boolean;
  allowClear?: boolean;
};
export type PasswordField = BaseField & {
  type: 'text';
  placeholder?: string;
  maxLength?: number;
};
export type TextLibField = BaseField & {
  type: 'textLib';
  min?: number;
  max?: number;
  limit?: number;
  symbol?: [string, string];
  dynamicTextLibRequest: () => Promise<any>;
  parseExcelRequest: () => Promise<any>;
  hideTip?: boolean;
  alertMessage?: string;
  hideImportExcel?: boolean;
  maxDynamicText?: number;
  editSingle?: boolean;
  keyText?: string;
  customTip?: () => string;
  formatValue?: (value: string) => string;
  extendedValidators?: {
    key: string;
    validate: (item: any) => boolean;
    errorText: string;
  };
};

export type TextareaField = BaseField & {
  type: 'textarea';
  placeholder?: string;
  maxLength?: number;
  autoSize?: boolean;
  showWordLimit?: boolean;
};
export type CascaderField = BaseField & {
  type: 'cascader';
  source: SelectSource;
  placeholder?: string;
  multiple?: boolean;
  maxTagCount?: number;
  pathMode?: boolean;
  fieldNames?: Record<string, string>;
  onPopupVisibleChange?: (visible: boolean, formData, value: any) => void;
  onClear?: ({ value, formData }) => void;
  onRemove?: ({ value, formData }) => void;
  formatLabel?: (options: CascaderOption[]) => string;
  valueKey?: string;
  popupContainer?: string;
  triggerProps?: any;
  checkable?: boolean;
  treeCheckStrictly?: boolean;
  hideLevel1Multiple?: boolean;
  labelColFlex?: string | number;
  checkStrictly?: boolean;
};
export type CascaderPanelField = BaseField & {
  type: 'cascader';
  source: SelectSource;
  placeholder?: string;
  multiple?: boolean;
  pathMode?: boolean;
  fieldNames?: Record<string, string>;
  searchPlaceholder?: string;
  panelTitle?: string;
  panelHeight?: string;
};
export type NumberField = BaseField & {
  type: 'number';
  placeholder?: string;
  allowClear?: boolean;
  suffix?: string;
  prefix?: string;
  mode?: 'embed' | 'button';
  hideNumber?: boolean;
  min?:
    | number
    | ((value: any, formData: Record<string, any>) => number | undefined);
  max?:
    | number
    | ((value: any, formData: Record<string, any>) => number | undefined);
  precision?: number;
  onBlur?: (value: any, formData: Record<string, any>) => void;
  separator?: string;
  rangeClear?: boolean;
};
export type SwitchField = BaseField & {
  type: 'switch';
  checkedValue: string | number | boolean;
  uncheckedValue: string | number | boolean;
};
export type BooleanField = BaseField & {
  type: 'boolean';
};

export type SelectTail = {
  type: 'reverse' | 'DButton' | 'DButtonGroup';
  props?: any;
  position: 'inner' | 'outer'; // 内部还是外部
  group?: DButton[];
};

export type SelectField = BaseField & {
  type: 'select' | 'cardSelect';
  format:
    | 'singleSelect'
    | 'multipleSelect'
    | 'radio'
    | 'buttonRadio'
    | 'cardRadio'
    | 'checkbox'
    | 'buttonCheckbox';
  source: SelectSource;
  placeholder?: string;
  labelTip?: string;
  select?: {
    showAll?: boolean;
    showAllText?: string;
    allowSearch?: boolean;
    allowClear?: boolean;
    allowCreate?: boolean;
    placeholder?: string;
    allowPaste?: boolean;
    limit?: number; // 多选时最多的选择个数
    maxTagCount?: number; // 多选模式下，最多显示的标签数量。0 表示不限制
    style?: Record<string, any>; // 样式
    valueKey?: string; // 设置object需要添加的key
    showExtraOptions?: boolean; // 是否在下拉菜单中显示额外选项
    formatLabel?: (data: SelectOptionData) => string; // 格式化显示的label
    cardTitle?: string; // 卡片标题
    filterOption?: boolean;
    isAsyncSearch?: boolean;
    loading?: boolean;
    search?: (word: string, formData: any) => void;
    popupVisibleChange?: (visible: boolean, formData) => void;
    onClear?: ({ value, formData }) => void;
    onRemove?: ({ value, formData, removed }) => void;
    tooltip?: boolean;
    reverse?: boolean; // 是否反选
    tail?: SelectTail; // 尾部配置
    openVirtual?: boolean; // 开启虚拟滚动
    direction?: 'vertical' | 'horizontal';
    bordered?: boolean;
    updateAtScroll?: boolean;
    max?: number;
    maxInputLength?: number;
    searchDelay?: number;
    emptyErrorOptions?: boolean;
    handleSourceError?: ({ value, formData, path, error }) => void;
  };
  getRef?: ({ formRef }) => any;
  card?: {
    width?: number | string;
  };
};

export type TransferField = BaseField & {
  type: 'transfer';
  source: SelectSource;
  oneWay?: boolean;
  simple?: boolean;
  loading?: boolean;
  remote?: boolean;
  search?: (value: string, formData: any) => any[];
  sourceTitle?: string | ((data: any) => string);
  targetTitle?: string | ((data: any) => string);
};

export type TransferSimpleField = Omit<TransferField, 'type'> & {
  type: 'transferSimple';
};

export type DateField = BaseField;

export type TimeRangeField = BaseField & {
  type: 'timeRange';
};

export type DateRangeField = BaseField & {
  showTime?: boolean;
  mode?: string;
  allowClear?: boolean;
  format?: 'timestamp' | 'Date' | string;
  valueFormat?: string;
  timePickerProps: Partial<TimePickerProps>;
  disabledDate?: (
    current: any,
    selectedDate: (string | undefined)[]
  ) => boolean;
  shortCutsList?: number[];
};
export type DatePickerField = BaseField & {
  type: 'datePicker';
  showTime?: boolean;
  position?: string;
  timeFormat?: string;
  disabledDate?: (current: Date) => any;
  disabledTime?: (current: Date) => any;
  disabled?: (value, formData) => boolean;
};
export type FileField = BaseField & {
  type: string;
  accept?: string;
  limit?: number;
  isImage: boolean;
  fileType?: string;
  fileTypeRange?: string;
  getFileInfo?: any;
  tips?: string;
  maxSize?: number;
  width?: number;
  height?: number;
  maxWidth?: number;
  maxHeight?: number;
  bucket?: string;
  path?: string;
  multiple?: boolean;
  obsKey?: {
    access_key_id: string;
    secret_access_key: string;
    server: string;
  };
  showOriginFileName?: boolean;
  uploadButton?: Component;
};

export type TreeSelectField = BaseField & {
  type: 'treeSelect';
  source: SelectSource;
  placeholder?: string;
  select?: {
    fullPath?: boolean;
    allowSearch?: boolean;
    allowClear?: boolean;
    multiple?: boolean;
    maxTagCount?: number;
    placeholder?: string;
    filterTreeNode: (searchKey: string, nodeData: any) => boolean;
    labelInValue?: boolean;
    treeProps?: Record<string, any>;
    checkable?: boolean;
    treeCheckStrictly?: boolean;
    createFunc?: (node: any) => void;
    canCreate?: ({ node, formData }) => boolean;
    createText?: string;
    style?: CSSProperties;
    customTitle?: (data: SelectSource['data']) => Component;
  };
};

export type TitleField = BaseField;

export type WildcardField = BaseField & {
  type: 'wildcard';
  source: SelectSource;
  placeholder?: string;
  maxLength?: number;
  showWordLimit?: boolean;
  readonly?: boolean;
  allowClear?: boolean;
  autoSize?: boolean | { minRows?: number; maxRows?: number };
};

// ==================== 以下是级联卡片的类型 ====================
export type CascaderCardField = BaseField & {
  type: 'cascaderCard';
  option:
    | Ref<CascaderOption[]>
    | CascaderOption[]
    | ((
        ...arg: ReactiveParams
      ) => Promise<CascaderOption[]> | CascaderOption[]);
  items: CascaderCardItem[] | ((data?: any) => any);
  combine?: boolean;
  getMapField?: (itemInfo: any) => any;
  loadMore?: (item: any, resolve: any) => void;
  valueStruct?: CascaderCardValueStruct; // 返回结果结构，array: 返回数组，tree: 返回树
  placeholder?: string;
  cardItemStyle?: Record<string, any>;
  isShowNumber?: boolean; // 是否显示数量（已选/全部）
  isReverse?: boolean; // 是否反选
  isShowValue?: boolean; // 是否展示 value
};

export type CascaderCardItem = {
  title: string;
  multiple?: boolean;
  isCheckAll?: boolean;
  canInput?: boolean;
  modalConfig?: {
    modalTitle?: string;
    fieldLabel?: string;
    customComponent?: Component;
    selectNewOption?: boolean; // 选中自定义新增选项
  };
};

export type CascaderCardValueStruct = 'array' | 'tree';
// ==================== 以上是级联卡片的类型 ====================
export type customViewField = {
  type: 'customView';
  label: string;
  name: string;
  contentStyle?: Record<string, any>;
  fields: Field[];
};
// ==================== 以下是展示卡片的类型 ====================
export type viewCardField = {
  type: 'viewCard';
  label: string;
  name: string;
  hideBorder?: boolean;
  contentStyle?: Record<string, any>;
  fields: Field[];
  extra: Component;
  description?: string;
};
// ==================== 以上是展示卡片的类型 ====================
// 多选 选择框
export type CheckSelectField = BaseField & {
  type: 'checkSelect';
  showAll?: boolean;
  source: {
    labelKey?: string;
    valueKey?: string;
    groupLabelKey?: string;
    groupValueKey?: string;
    childrenKey?: string;
    data: Option[] | ((...arg: ReactiveParams) => Promise<Option[]> | Option[]);
  };
  placeholder?: string;
};

export type PrimaryField =
  | TextField
  | NumberField
  | SelectField
  | BooleanField
  | FileField
  | TextareaField
  | CascaderField
  | DateRangeField
  | TransferField
  | TreeSelectField
  | WildcardField
  | TitleField
  | DatePickerField
  | CascaderCardField
  | viewCardField
  | TransferSimpleField;

export type ArrayField = BaseField & {
  type: 'array';
  // eslint-disable-next-line no-use-before-define
  item: ObjectField | ArrayField;
  format?: 'table' | 'list' | 'card' | Component;
  params?: Record<string, any>;
  defaultItem?: Record<string, any> | (() => Record<string, any>);
  viewType?: 'card';
  rowKey?: string | number;
  hideOperation?: boolean;
  childItemLabel?: string;
  isUnshift?: boolean;
  minLength?:
    | number
    | ((value: any, formData: Record<string, any>) => number | undefined);
  maxLength?:
    | number
    | ((value: any, formData: Record<string, any>) => number | undefined);
  canRemoveFn?: (
    value: any,
    formData: Record<string, any>,
    index: number
  ) => boolean;
  tableProps?: any;
  columnProps?: any;
  canCopy?: boolean;
  ignoredCopyFields?: string[];
};

export type ObjectField = BaseField & {
  type: 'object';
  // eslint-disable-next-line no-use-before-define
  fields: Field[];
  layout?: 'vertical' | 'inline' | 'horizontal';
  viewType?: 'card';
  gutter?: number;
  footerExtra?: Component;
};

export type CustomField = BaseField & {
  component: Component;
  params: any;
};

export type Field = ObjectField | ArrayField | PrimaryField | CustomField;

export type FormSchema = {
  fields: Field[];
};

export type FormData = Record<string, any>;

export type ArcoFormProps = {
  size?: string;
  style?: Record<string, any>;
  labelColProps?: Record<string, any>;
  wrapperColProps?: Record<string, any>;
  labelAlign?: 'left' | 'right';
  scrollToFirstError?: boolean;
};

export type DForm = {
  formSchema: FormSchema;
  disabled?: boolean | ((formData: Record<string, any>) => boolean);
  defaultFormData?: FormData;
  resetFormData?: FormData;
  layout?: 'vertical' | 'inline' | 'horizontal';
  arcoFormProps?: ArcoFormProps;
  isScrollToError?: any;
  onEnter?: ({ formData, formRef }) => void;
  upRender?: boolean;
};
