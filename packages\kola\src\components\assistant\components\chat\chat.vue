<template>
  <div class="mask" id="chat">
    <div class="drag-handle" @mousedown="startDragging">诚优小助手</div>
    <feedback ref="feedbackRef" />
    <a-tooltip content="意见反馈" popup-container="#chat">
      <a-button
        @click="handleAdvice"
        class="advice-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-edit size="13" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip content="历史" popup-container="#chat">
      <a-button
        @click="handleHistory"
        class="history-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-clock-circle size="13" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip content="关闭" popup-container="#chat">
      <a-button
        @click="handleClose"
        class="close-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-close size="13" />
        </template>
      </a-button>
    </a-tooltip>
    <div class="scroll-wrapper" ref="scrollWrapper">
      <!-- <RecordsLine v-if="showRecordsLine" @expand="handleExpand" /> -->
      <Greeting v-if="showQuick" />
      <tagOperation v-if="showQuick" />
      <Message
        v-for="item in messageList"
        :key="item.id"
        :id="item.id"
        :type="item.type"
        :message="item.message"
        :gradually="item.gradually"
        :scroll-to-bottom="item.scrollToBottom"
        :params="item.params || undefined"
        :question="item.question"
        :show-stamp="item.showStamp"
      />
    </div>
    <QuickOperation @question="handleQuestion" />
    <InputBox
      class="input-box"
      ref="inputBoxRef"
      @submit="handleSubmit"
      @choose="handleChoose"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, provide, inject, Ref, computed } from 'vue';
  import InputBox from './input-box.vue';
  import QuickOperation from './quick-operation.vue';
  import tagOperation from './tag-operation.vue';
  import Greeting from './greeting.vue';
  // import RecordsLine from './records-line.vue';
  import Message from './message.vue';
  import { MessageType } from '../../types';
  import Feedback from '../feedback.vue';
  import { useDragging } from '../../utils/dragging';
  // import useChatRecord from '../../hooks/use-chat-record';
  import { useUpdateTime } from '../../hooks/use-update-time';

  const { addTimeStamp } = useUpdateTime();
  const scrollWrapper = ref<HTMLElement | null>(null);
  const feedbackRef = ref<HTMLElement | null>(null);
  const inputBoxRef = ref<HTMLElement | null>(null);
  const messageList = ref<MessageType[]>([]);
  const chartWrapper = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'chartWrapper'
  );
  // const { chatRecord, nextPage } = useChatRecord();
  provide('scrollWrapper', scrollWrapper);
  provide('feedbackRef', feedbackRef);
  provide('inputBoxRef', inputBoxRef);
  const { startDragging } = useDragging(chartWrapper);
  // const expanded = ref(false);
  // const showRecordsLine = computed(() => {
  //   return chatRecord.value.length > 0 && !expanded.value;
  // });
  const showQuick = computed(() => {
    return messageList.value.length === 0;
  });
  const emit = defineEmits(['close', 'history', 'advice', 'question']);
  // @ts-ignore
  const mode = process.env.VITE_MODE;
  console.log('MODE:', mode);
  const handleClose = () => {
    emit('close');
  };
  const handleHistory = () => {
    emit('history');
  };
  const handleAdvice = () => {
    emit('advice');
  };
  const handleQuestion = () => {
    emit('question');
  };
  const handleChoose = (item: any) => {
    handleSubmit(item);
  };
  // const handleExpand = () => {
  //   messageList.value.unshift(...chatRecord.value);
  //   expanded.value = true;
  //   nextTick(() => {
  //     if (scrollWrapper.value) {
  //       scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
  //     }
  //   });
  // };
  const handleSubmit = (value: any) => {
    const time = Date.now();
    const lastTime = messageList.value[messageList.value.length - 1]?.id;
    let showStamp = false;
    if ((lastTime && time - lastTime > 30000) || !lastTime) {
      showStamp = true;
      addTimeStamp(time);
    }
    messageList.value.push({
      id: time,
      type: 'my',
      message: typeof value === 'string' ? value : value.question,
      showStamp,
    });
    messageList.value.push({
      id: time + 1,
      type: 'assistant',
      message: '',
      params: {
        qtype: typeof value === 'string' ? 2 : 1,
        question: typeof value === 'string' ? value : value.question,
        libraryId: typeof value === 'string' ? undefined : value.libraryId,
      },
      gradually: true,
      scrollToBottom: true,
      question: typeof value === 'string' ? value : value.question,
    });
  };
  // const handleScroll = () => {
  //   if (scrollWrapper.value?.scrollTop === 0) {
  //     const previousHeight = scrollWrapper.value.scrollHeight;
  //     nextPage(() =>
  //       nextTick(() => {
  //         if (scrollWrapper.value) {
  //           const newHeight = scrollWrapper.value.scrollHeight;
  //           scrollWrapper.value.scrollTop = newHeight - previousHeight;
  //         }
  //       })
  //     );
  //   }
  // };
  defineExpose({
    handleChoose,
  });
</script>

<style scoped>
  .close-button {
    position: absolute;
    top: 18px;
    right: 18px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .close-button:hover:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .history-button {
    position: absolute;
    top: 18px;
    right: 48px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .history-button:hover:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .advice-button {
    position: absolute;
    top: 18px;
    right: 78px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .advice-button:hover:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .drag-handle {
    cursor: move;
    padding: 18px 23px;
    padding-bottom: 10px;
    user-select: none;
    width: calc(100% - 108px);
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000;
  }

  .input-box {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .quick-operation {
    width: 100%;
    position: absolute;
    bottom: 108px;
    left: 0;
  }

  .scroll-wrapper {
    overflow-y: auto;
    width: 100%;
    height: calc(100% - 190px);
  }
</style>
