import { defineStore } from 'pinia';
import { cloneDeep, pick } from 'lodash';
import { setToken, clearToken, getToken } from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import commonApi from '@/api/common';
import tradeApi from '@/api/trade';
import { UserState } from './types';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    avatar: '',
    id: '',
    name: '',
    balance: 0,
  }),

  getters: {
    userInfo(state: UserState): UserState {
      return { ...state };
    },
  },

  actions: {
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async info() {},

    async login(data: any) {
      try {
        const res = await commonApi.login(data);
        this.setInfo(res.data);
        setToken(res.data.token);
      } catch (error) {
        clearToken();
        throw error;
      }
    },

    async accountDetail() {
      // const res = await commonApi.accountDetail();

      if (getToken()) {
        const res = await Promise.all([
          commonApi.accountDetail(),
          tradeApi.stats(),
        ]);
        const [accountDetail, tradeStats] = res;

        this.setInfo({
          ...accountDetail.data,
          ...tradeStats.data,
        });
      }
    },
  },
});

export default useUserStore;
