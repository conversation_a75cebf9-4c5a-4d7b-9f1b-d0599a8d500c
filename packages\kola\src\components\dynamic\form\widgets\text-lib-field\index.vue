<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="rules"
  >
    <TitleTextarea
      ref="textLibRef"
      v-model="value"
      :min="field.min"
      :max="field.max"
      :limit="field.limit"
      :symbol="field.symbol"
      :hide-tip="hideTip"
      :hide-import-excel="hideImportExcel"
      :show-dynamic-text="field.showDynamicText"
      :alert-message="field.alertMessage"
      :max-dynamic-text="field.maxDynamicText"
      :parse-excel-request="field.parseExcelRequest"
      :dynamic-text-lib-request="field.dynamicTextLibRequest"
      :text-lib-columns="field.textLibColumns"
      :edit-single="field.editSingle"
      :display-title="field.displayTitle"
      :placeholder-text="field.placeholderText"
      :key-text="keyText"
      :custom-tip="field.customTip"
      :format-value="field.formatValue"
      :extended-validators="field.extendedValidators"
    ></TitleTextarea>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref, watchEffect } from 'vue';
  import { isFunction } from 'lodash';
  import { TextLibField } from '../../../types/form';
  import TitleTextarea from './title-textarea/index.vue';

  const props = defineProps<{
    field: TextLibField;
    path: string;
  }>();
  const keyText = computed(() => {
    return props.field.keyText ?? '文案';
  });
  const textLibRef = ref(null);
  const formData = inject('formData');
  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const hideImportExcel = computed(() => {
    return isFunction(props.field.hideImportExcel)
      ? props.field.hideImportExcel(
          props.field,
          formData?.value as any,
          props.path
        )
      : props.field.hideImportExcel;
  });
  const hideTip = computed(() => {
    return isFunction(props.field.hideTip)
      ? props.field.hideTip(props.field, formData?.value as any, props.path)
      : props.field.hideTip;
  });
  const value = defineModel<string[]>({
    default: [],
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });

  const rules = computed(() => {
    return [...props.field.rules, { type: 'array', validator }];
  });

  function validator(val, cb) {
    const inValid = textLibRef.value?.validateAll();
    cb(inValid ? ' ' : '');
  }
</script>
