{"name": "aggregation", "description": "AI 聚合中台", "version": "1.0.0", "private": true, "author": "zzj", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "build:hw-prod": "VITE_MODE=production pnpm run build", "build:hw-test": "VITE_MODE=testing pnpm run build", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@repo/eslint-config": "workspace:*", "@repo/kola": "workspace:*", "@repo/sdk": "workspace:*", "@repo/typescript-config": "workspace:*", "github-markdown-css": "^5.8.1", "go-captcha-vue": "^2", "js-sha256": "^0.11.1", "markdown-it": "^14.1.0"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"madge": "^8.0.0", "rollup-plugin-visualizer": "^5.12.0"}}