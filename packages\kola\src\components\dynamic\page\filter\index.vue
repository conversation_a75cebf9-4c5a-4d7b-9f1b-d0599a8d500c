<template>
  <div ref="filterRef">
    <DynamicForm
      :form-schema="formSchema"
      v-model="filter"
      :reset-form-data="props.resetFormData"
      layout="inline"
      ref="formRef"
      class="dynamic-filter-form"
    >
      <template #footer>
        <div class="filter-footer">
          <a-button
            type="primary"
            @click="query"
            class="search-btn"
            :disabled="filterDisabled"
          >
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="reset-btn" :disabled="filterDisabled">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
          <a-button
            @click="handleCollapse"
            type="text"
            v-if="showCollapseBtn"
            :disabled="filterDisabled"
          >
            {{ collapsed ? '展开' : '收起' }}
            <icon-down v-if="collapsed" />
            <icon-up v-else />
          </a-button>
        </div>
      </template>
    </DynamicForm>
  </div>
</template>

<script setup lang="ts">
  import {
    computed,
    ref,
    onMounted,
    nextTick,
    onUnmounted,
    useTemplateRef,
    onBeforeUnmount,
    inject,
  } from 'vue';
  import { cloneDeep, debounce, forEach, isFunction, isNil } from 'lodash';
  import DynamicForm from '../../form/index.vue';
  import { DForm } from '../../types/form';
  import useDispatch from '../../hooks/dispatch';
  import { DPage } from '@/components/dynamic/types/page';

  defineOptions({ name: 'DynamicFilter' });

  const props = defineProps<{
    formSchema: DForm['formSchema'];
    resetFormData?: DForm['resetFormData'];
    onSearch?: any;
    onReset?: any;
    disabled?: any;
  }>();
  const filter = defineModel<Record<string, any>>({ default: {} });

  const formRef = ref();
  const dispatch = useDispatch();
  const collapsed = ref(false);
  const firstRowLength = ref(4);
  const showCollapseBtn = computed(() => {
    return props.formSchema.fields.length >= firstRowLength.value;
  });
  const getCustomParams = inject<DPage['getCustomParams']>('getCustomParams');
  const filterDisabled = computed(() => {
    const { disabled } = props;
    if (isFunction(disabled)) {
      return disabled({
        filter: filter.value,
        getCustomParams,
      });
    }
    return false;
  });

  function handleCollapse() {
    collapsed.value = !collapsed.value;
    if (showCollapseBtn.value) {
      setElementDisplay();
    }
  }

  const query = () => {
    dispatch({
      type: 'refreshTable',
      payload: { resetPagination: true, resetPageSize: true },
    });
    props.onSearch?.();
  };

  const reset = () => {
    formRef.value?.reset();
    dispatch({
      type: 'refreshTable',
      payload: { resetPagination: true },
    });
    props.onReset?.();
  };

  const filterRef = useTemplateRef<HTMLElement>('filterRef');
  const computedShowLength = debounce(() => {
    const width = window.innerWidth;
    if (width > 1440) {
      firstRowLength.value = 4;
    } else if (width > 1280) {
      firstRowLength.value = 3;
    } else {
      firstRowLength.value = 2;
    }
    if (showCollapseBtn.value) {
      setElementDisplay();
    }
  }, 50);

  const hideClass = 'page-filter-hide-element';
  function setElementDisplay() {
    nextTick(() => {
      const formItems: any = filterRef.value?.querySelectorAll(
        `.dynamic-filter-form >.arco-form-item`
      );
      if (formItems?.length > 0) {
        const collapsedValue = collapsed.value;
        if (collapsedValue) {
          forEach(formItems, (forItemElement, index: number) => {
            if (index >= firstRowLength.value - 1) {
              forItemElement.classList.add(hideClass);
            }
          });
        } else {
          forEach(formItems, (forItemElement) => {
            if (forItemElement.classList.contains(hideClass)) {
              forItemElement.classList.remove(hideClass);
            }
          });
        }
      }
    });
  }
  computedShowLength();
  window.addEventListener('resize', computedShowLength);
  onBeforeUnmount(() => {
    window.removeEventListener('resize', computedShowLength);
  });
</script>

<style scoped lang="less">
  .filter-footer {
    display: flex;
    gap: 16px;
    justify-content: end;
    margin-left: auto;
    height: 48px;
  }

  @xl: 1440px;
  @lg: 1280px;
  @baseMarginRight: 16px;

  .dynamic-filter-form {
    //16x 是form右边距
    :deep(> div) {
      margin-right: @baseMarginRight;

      @media (min-width: @xl) {
        .searchWidthFn(25%);
      }

      @media screen and (min-width: @lg) and (max-width: @xl) {
        .searchWidthFn(33.3%);
      }

      @media screen and (max-width: @lg) {
        .searchWidthFn(50%);
      }
    }
  }
  .searchWidthFn(@baseWidth) {
    width: calc(@baseWidth - @baseMarginRight) !important;
  }
</style>

<style>
  .page-filter-hide-element {
    display: none !important;
  }
</style>
