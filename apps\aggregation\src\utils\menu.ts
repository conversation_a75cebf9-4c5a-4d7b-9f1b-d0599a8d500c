/* eslint-disable import/prefer-default-export */
import type { RouteRecordRaw } from 'vue-router';

interface MenuItem {
  key: string;
  title: string;
  icon?: string;
  children?: MenuItem[];
  path: string;
}

export function generateMenuItems(routes: RouteRecordRaw[]): MenuItem[] {
  return routes
    .filter((route) => route.meta?.title && !route.meta.hideInMenu) // 只处理有标题的路由
    .sort((a, b) => {
      const orderA = a?.meta?.order ?? Number.MAX_SAFE_INTEGER;
      const orderB = b?.meta?.order ?? Number.MAX_SAFE_INTEGER;
      return orderA - orderB;
    })
    .map((route) => ({
      key: route.name as string,
      title: route.meta?.title as string,
      icon: route.meta?.icon as string,
      path: route.path,
      children: route.children ? generateMenuItems(route.children) : undefined,
    }));
}
