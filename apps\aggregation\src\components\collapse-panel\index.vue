<template>
  <a-spin class="collapse-panel" :loading="loading">
    <div class="collapse-panel-head">
      <div class="collapse-panel-title">
        <slot name="title">{{ props.title }}</slot>
      </div>
      <div class="collapse-panel-extra">
        <slot name="extra"></slot>
        <span
          class="collapse-icon"
          @click="handleCollapse"
          v-if="props.canCollapse"
        >
          <icon-caret-down v-show="!isCollapse" />
          <icon-caret-right v-show="isCollapse" />
        </span>
      </div>
    </div>
    <div
      class="collapse-panel-body"
      :class="isCollapse ? 'collapse' : 'expand'"
    >
      <slot></slot>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    canCollapse: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });
  const isCollapse = ref(false);
  function handleCollapse() {
    isCollapse.value = !isCollapse.value;
  }
</script>

<style scoped lang="less">
  .collapse-panel {
    display: block;
    background: #fff;
    border-radius: 2px;
    border: 0 solid rgb(0 0 0 / 8%);

    .collapse-panel-head {
      display: flex;
      padding: 0 24px;
      height: 64px;
      align-items: center;
      border: 1px solid rgb(0 0 0 / 8%);

      .collapse-panel-title {
        font-weight: 500;
        font-size: 16px;
        color: rgb(0 0 0 / 84%);
        line-height: 24px;
      }

      .collapse-panel-extra {
        margin-left: auto;
        display: flex;
        align-items: center;

        .collapse-icon {
          margin-left: 8px;
          cursor: pointer;
          width: 20px;
          height: 20px;
          text-align: center;
          line-height: 20px;
          border-radius: 50%;

          &:hover {
            background-color: var(--color-fill-2);
          }
        }
      }
    }

    .collapse-panel-body {
      overflow: hidden;
      border: 1px solid rgb(0 0 0 / 8%);

      &.collapse {
        max-height: 0;
        border: none;
      }

      &.expand {
        border-top: none;
      }
    }
  }
</style>
