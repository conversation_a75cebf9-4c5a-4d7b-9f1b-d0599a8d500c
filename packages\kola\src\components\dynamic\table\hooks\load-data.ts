import { Ref, inject } from 'vue';
import useLoading from '@repo/sdk/hooks/loading';
import { DPage } from '../../types/page';

const useLoadData = ({ pagination, loadAction, isPageable = true, sorter }) => {
  const { loading, setLoading } = useLoading(false);

  const filter = inject<Ref<any>>('filter');
  const getCustomParams = inject<DPage['getCustomParams']>('getCustomParams');
  async function loadData() {
    setLoading(true);
    try {
      const { data } = await loadAction(
        filter?.value ?? {},
        isPageable
          ? { pageNum: pagination.current, pageSize: pagination.pageSize }
          : {},
        getCustomParams?.(),
        sorter.sortDirection ? sorter : undefined
      );
      return data;
    } catch (err) {
      return Promise.reject(err);
    } finally {
      setLoading(false);
    }
  }

  return { loadData, loading };
};

export default useLoadData;
