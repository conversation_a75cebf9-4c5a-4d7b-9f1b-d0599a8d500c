<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    @ok="modalSubmit"
    :ok-loading="loading"
    @cancel="closeModal"
  >
    <a-spin :loading="loading" style="width: 100%">
      <DynamicForm
        ref="formRef"
        :form-schema="computedFormSchema"
        :default-form-data="defaultFormData"
        v-model="formDataModel"
      />
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { cloneDeep, isFunction } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import useLoading from '@/hooks/loading';

  const emits = defineEmits(['success']);
  const props = defineProps({
    type: {
      type: String,
    },
    row: {
      type: Object,
      default: () => {
        return {};
      },
    },
    formSchema: {
      type: [Object, Function],
      default: () => {
        return {};
      },
    },

    formData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    operation: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const visible = defineModel<boolean>();
  const modalTitle = computed(() => {
    const { operation } = props;
    return visible.value ? `${operation.title}` : '';
  });
  const modalType = computed(() => {
    const { type } = props;
    return {
      isCreate: type === 'create',
      isModify: type === 'modify',
      isCopy: type === 'copy',
    };
  });
  const computedFormSchema = computed(() => {
    return isFunction(props.formSchema)
      ? props.formSchema(modalType.value)
      : props.formSchema ?? {};
  });
  const formDataModel = ref({});
  const defaultFormData = ref({});
  watch(
    () => visible.value,
    (newVal) => {
      if (newVal) {
        formDataModel.value = cloneDeep(props?.formData ?? {});
        defaultFormData.value = cloneDeep(props?.formData ?? {});
        setFormData();
      }
    }
  );

  async function setFormData() {
    const { isCreate } = modalType.value;
    if (isCreate) {
      return;
    }

    if (!props.operation.fetchDetail) {
      return;
    }

    try {
      setLoading(true);
      const { data } = await props.operation.fetchDetail(
        props.row,
        modalType.value
      );
      Object.assign(formDataModel.value, data);
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('e', e);
    } finally {
      setLoading(false);
    }
  }

  const { loading, setLoading } = useLoading();
  const formRef = ref();

  async function modalSubmit() {
    try {
      await formRef.value.validate();
      setLoading(true);
      await props.operation.action(formDataModel.value);
      Message.success(`${modalTitle.value}成功`);
      emits('success');
      closeModal();
    } catch (e: any) {
      if (e.type === 'form') {
        Message.warning('表单未填写完整');
      }
    } finally {
      setLoading(false);
    }
  }

  function closeModal() {
    visible.value = false;
    setLoading(false);
    formRef.value.reset();
  }
</script>
