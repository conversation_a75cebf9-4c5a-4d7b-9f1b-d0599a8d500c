<template>
  <header class="app-header">
    <!-- Logo -->
    <div class="logo">
      <LogoIcon />
    </div>

    <!-- Navigation -->
    <nav class="nav">
      <ul>
        <li
          v-for="item in navItems"
          :key="item.label"
          :class="{ active: active.includes(item.href) }"
        >
          <router-link :to="item.href">{{ item.label }}</router-link>
        </li>
      </ul>
    </nav>

    <!-- Actions -->
    <div class="actions">
      <img
        src="@/assets/images/icon_1.png"
        alt="icon"
        style="width: 22px; height: 20px; cursor: pointer"
        @click="handleIconClick"
      />
      <section v-if="!isLogin" class="content">
        <button class="btn solid" @click="handleLogin">{{ loginText }}</button>
      </section>
      <section v-else class="content">
        <div class="account">
          <span>U点</span>
          <span class="account-number">{{ userInfo.balance }}</span>
        </div>
        <a-popover
          trigger="click"
          :content-style="{
            width: '212px',
            boxShadow: '0px 0px 30px 0px rgba(0,0,0,0.04)',
            padding: '0',
            border: 'unset',
          }"
          position="br"
          :arrow-style="{
            borderColor: 'transparent',
          }"
          popup-container="#avatar"
        >
          <a-avatar id="avatar" class="account-avatar">
            <img
              :src="userInfo.avatar"
              alt="avatar"
              style="transform: scale(1.1)"
            />
          </a-avatar>
          <template #content>
            <div>
              <div class="account-menu-header">
                <a-avatar :size="36">
                  <img :src="userInfo.avatar" alt="avatar" />
                </a-avatar>
                <div class="account-info">
                  <text>{{ userInfo.name }}</text>
                  <text>用户ID：{{ userInfo.id }}</text>
                </div>
              </div>
              <div class="divider">
                <a-divider margin="0" />
              </div>
              <div class="popover_btn_list">
                <div
                  v-for="item in featureList"
                  :key="item.title"
                  class="popover_btn"
                  @click="handleSelect(item.title)"
                >
                  {{ item.title }}
                </div>
              </div>
            </div>
          </template>
        </a-popover>
      </section>
    </div>
  </header>
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { clearToken, getToken } from '@/utils/auth';
  import { useUserStore, useHomeConfigStore } from '@/store';
  import LogoIcon from '@/assets/images/logo.svg?component';
  import { Modal } from '@arco-design/web-vue';

  interface NavItem {
    label: string;
    href: string;
  }

  const router = useRouter();
  const userStore = useUserStore();
  const homeConfigStore = useHomeConfigStore();
  const active = computed(() => router.currentRoute.value.path);

  const navItems: NavItem[] = [
    {
      label: '首页',
      href: '/home',
    },
    {
      label: '应用市场',
      href: '/app',
    },
    {
      label: '成功案例',
      href: '/case',
    },
  ];
  const loginText = '登 录';
  const featureList = [
    // {
    //   title: '我的应用',
    //   href: '/account/apps',
    // },
    {
      title: 'APIKey管理',
      href: '/account/ak',
    },
    {
      title: '充值中心',
      href: '/account/recharge',
    },
    {
      title: '消费统计',
      href: '/account/consumption',
    },
    { title: '退出登录' },
  ];
  const userInfo = computed(() => userStore.userInfo);

  const isLogin = !!getToken();

  const handleLogin = () => {
    router.push('/login');
  };

  const handleSelect = (value: any) => {
    const item = featureList.find((i) => i.title === value);
    if (!item) return;
    if (item.href) {
      router.push(item.href);
    } else if (item.title === '退出登录') {
      Modal.confirm({
        title: '提示',
        content: '确定退出登录吗？',
        onOk: () => {
          clearToken();
          userStore.resetInfo();
          window.location.href = '/home';
        },
      });
    }
  };

  const handleIconClick = () => {
    window.open(homeConfigStore.homeConfig.access_link, '_blank');
  };

  onMounted(() => {
    userStore.accountDetail();
    homeConfigStore.getHomeConfig();
  });
</script>

<style scoped lang="less">
  .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 48px;
    height: 64px;
    background: #fff;
    border-bottom: 1px solid #f2f2f2;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .logo img {
    width: 190px;
    height: 25px;
  }

  .nav ul {
    display: flex;
    gap: 32px;
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .nav a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
  }

  .nav li.active a,
  .nav a:hover {
    color: #ff7f00; /* 激活色和 hover 色 */
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .content {
      display: flex;
      align-items: center;
      gap: 12px;

      .account {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 36px;
        padding: 7px 16px;
        border: 1px solid #ff9e00;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        color: rgb(0 0 0 / 84%);
        line-height: 22px;

        .account-number {
          color: #ff7d00;
        }
      }
    }
  }

  .account-menu-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 24px;
    padding-bottom: 0;
    padding-top: 12px;
  }

  .divider {
    padding: 4px 24px;
  }

  .popover_btn_list {
    display: flex;
    flex-direction: column;

    .popover_btn {
      width: 100%;
      border: none;
      font-weight: 400;
      font-size: 16px;
      color: rgb(0 0 0 / 84%);
      line-height: 22px;
      text-align: left;
      cursor: pointer;
      padding: 12px 24px;

      &:hover {
        background: #f6f8fa;
      }
    }
  }

  .btn {
    width: 93px;
    height: 40px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn.ghost {
    color: #333;
    background: transparent;
  }

  .btn.solid {
    color: #fff;
    background: #ff7f00;
  }

  .btn.ghost:hover {
    border-color: #ff7f00;
    color: #ff7f00;
  }

  .btn.solid:hover {
    opacity: 0.85;
  }

  .account-avatar {
    width: 36px;
    height: 36px;
    cursor: pointer;

    .account-info {
      display: flex;
      flex-direction: column;
    }

    :deep(.arco-popover-content) {
      margin-top: 0;
    }
  }
</style>
