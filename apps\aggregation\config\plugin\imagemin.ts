/**
 * Image resource files used to compress the output of the production environment
 * 图片压缩
 * https://github.com/anncwb/vite-plugin-imagemin
 */
import viteImagemin from 'vite-plugin-imagemin';

export default function configImageminPlugin() {
  const imageminPlugin = viteImagemin({
    gifsicle: {
      optimizationLevel: 7,
      interlaced: false,
    },
    // optipng: {
    //   optimizationLevel: 7,
    // },
    mozjpeg: {
      quality: 50,
    },
    pngquant: {
      quality: [0.7, 0.8],
      speed: 7,
    },
    svgo: {
      plugins: [
        {
          name: 'removeViewBox',
        },
        {
          name: 'removeEmptyAttrs',
          active: false,
        },
      ],
    },
  });
  return {
    ...imageminPlugin,
    name: 'timed-imagemin',
    async generateBundle(...args) {
      const start = Date.now();
      if (typeof imageminPlugin.generateBundle === 'function') {
        await imageminPlugin.generateBundle.apply(this, args);
      }
      const duration = Date.now() - start;
      console.log(`[imagemin] 图片压缩耗时: ${duration / 1000}s`);
    },
  };
}
