<template>
  <div class="scene-section">
    <span class="scene-title">客户的选择，见证真实效果</span>
    <CaseCard :case-list="caseList" />
  </div>
</template>

<script setup lang="ts">
  import CaseCard from '@/components/common/CaseCard.vue';

  const { caseList } = defineProps<{
    caseList: {
      id: number;
      image: string;
      name: string;
      desc: string;
    }[];
  }>();
</script>

<style scoped lang="less">
  .scene-section {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .scene-title {
    display: block;
    height: 44px;
    color: rgb(0 0 0 / 100%);
    font-size: 32px;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 44px;
    margin: 80px 0 48px;
  }
</style>
