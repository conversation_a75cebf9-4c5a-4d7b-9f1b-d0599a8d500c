export interface AnyObject {
  [key: string]: unknown;
}

export interface Options {
  value: unknown;
  label: string;
}

export interface NodeOptions extends Options {
  children?: NodeOptions[];
}

export interface GetParams {
  body: null;
  type: string;
  url: string;
}

export interface PostData {
  body: string;
  type: string;
  url: string;
}

export interface Pagination {
  current: number;
  pageSize: number;
  total?: number;
  showPageSize?: boolean;
  showTotal?: boolean;
}

export type TimeRanger = [string, string];

export interface GeneralChart {
  xAxis: string[];
  data: Array<{ name: string; value: number[] }>;
}

export interface API {
  list?: (data?: any) => Promise<any>;
  toggleStatus?: (data?: any) => Promise<any>;
  create?: (data?: any) => Promise<any>;
  modify?: (data?: any) => Promise<any>;
  copy?: (data?: any) => Promise<any>;
  remove?: (data?: any) => Promise<any>;
  detail?: (data?: any) => Promise<any>;
  batchRemove?: (data?: any) => Promise<any>;
  batchUpload?: (data?: any) => Promise<any>;
  batchEdit?: (data?: any) => Promise<any>;
  dict?: (data?: any) => Promise<any>;
}

// window api 配置
declare global {
  interface Window {
    $api: Record<string, any>;
  }
}
