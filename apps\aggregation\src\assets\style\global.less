@import './dpage.less';
* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}
body {
  min-width: 1280px;
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    width: 164px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    margin: 0;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.arco-btn,
.arco-picker,
.arco-input-wrapper,
.arco-select-view-single,
.arco-radio-group-button,
.arco-select-view-multiple,
.arco-radio-button {
  border-radius: var(--border-radius-medium);
}

.arco-pagination-options {
  .arco-select-view-single {
    background-color: #fff;
    border: 1px solid #dfe0e2;
  }
}

.arco-pagination-jumper {
  .arco-input-wrapper {
    background-color: #fff;
    border: 1px solid #dfe0e2;
  }
}

.general-card {
  border-radius: 4px !important;
  border: none !important;
  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }
  & > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-header,
.arco-table-th,
.arco-table-td:hover,
.arco-table-hover:not(.arco-table-dragging)
  .arco-table-tr:not(.arco-table-tr-empty):not(.arco-table-tr-summary):hover
  .arco-table-td.arco-table-col-fixed-left::before {
  background: #fbfcfc;
}

.arco-table-tfoot {
  background: unset;
  scrollbar-color: unset;
  border-top: unset;
}

.arco-table-th,
.arco-table-td {
  border-bottom: 1px solid #f4f4f5;
}

.arco-table-hover:not(.arco-table-dragging)
  .arco-table-tr:not(.arco-table-tr-empty):not(.arco-table-tr-summary):hover
  .arco-table-td:not(.arco-table-col-fixed-left):not(
    .arco-table-col-fixed-right
  ),
.arco-table-hover
  .arco-table-tr-drag
  .arco-table-td:not(.arco-table-col-fixed-left):not(
    .arco-table-col-fixed-right
  ) {
  background: #fbfcfc;
}

.arco-table-cell {
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgb(var(--blue-6));
    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

.arco-menu-light .arco-menu-inline-header,
.arco-menu-light .arco-menu-item {
  color: rgb(var(--gray-9));
}

.arco-menu-light
  .arco-menu-inline-content
  .arco-menu-item:not(.arco-menu-selected) {
  color: rgb(var(--color-red));
}

.arco-menu-light .arco-menu-inline-header .arco-icon,
.arco-menu-light .arco-menu-item .arco-icon {
  color: rgb(var(--gray-9));
}

.arco-form-layout-inline .arco-form-item {
  margin-bottom: 16px;
}

.arco-form-item-label-col > .arco-form-item-label {
  font-size: 13px;
}

.arco-input-wrapper .arco-input.arco-input-size-medium {
  font-size: 13px;
}

.arco-select-view-single.arco-select-view-size-medium .arco-select-view-input,
.arco-select-view-single.arco-select-view-size-medium .arco-select-view-value {
  font-size: 13px;
}

.arco-picker-size-medium input {
  font-size: 13px;
}

.arco-btn-size-medium {
  font-size: 13px;
}

.arco-table .arco-table-td {
  font-size: 13px;
  height: 56px;
}

.arco-table .arco-table-th {
  font-size: 13px;
  height: 56px;
}

// 搜索框 的高度最小 32
.arco-form-item-content > span {
  min-height: 32px;
}

.hide-element {
  display: none !important;
}

.arco-menu-selected {
  color: var(--color-red);
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s ease-out;
}
.fade-transform-enter-from {
  opacity: 0;
  transition: all 0.2s ease-out;
}
.fade-transform-leave-to {
  opacity: 0;
  transition: all 0.2s ease-out;
}

.g-scroll-bar {
  &::-webkit-scrollbar {
    width: 9px;
    height: 9px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.4);
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.4);
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(50, 50, 50, 0.1);
  }

  &::-webkit-scrollbar-track:hover {
    background-color: rgba(50, 50, 50, 0.2);
  }
}

:root {
  --color-red: #ff0018;
}

#app {
  min-height: 100%;
}

::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.4);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.6);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-track:hover {
  background-color: rgba(50, 50, 50, 0.2);
}
