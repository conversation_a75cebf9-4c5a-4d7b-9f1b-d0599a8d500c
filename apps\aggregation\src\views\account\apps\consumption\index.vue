<template>
  <a-modal
    v-model:visible="visible"
    title="消耗U点明细"
    width="800px"
    title-align="start"
    :footer="false"
  >
    <DynamicTable :table="tableConfig" hide-selection />
  </a-modal>
</template>

<script setup lang="tsx">
  import userAppApi from '@/api/user-app';

  const visible = defineModel<boolean>('visible', { required: true });
  const props = defineProps<{
    appId: string;
  }>();

  const tableConfig = {
    columns: [
      {
        title: '消耗时间',
        dataIndex: 'consume_time',
      },
      {
        title: '消耗类型',
        dataIndex: 'consume_type',
      },
      {
        title: '任务详情',
        dataIndex: 'task_detail',
      },
      {
        title: '消耗U点',
        dataIndex: 'consume_u_point',
      },

      {
        title: '任务ID',
        dataIndex: 'task_id',
      },
    ],

    load: {
      action: async (_filter, pageInfo) => {
        const pageParams = {
          page: pageInfo.pageNum,
          limit: pageInfo.pageSize,
        };
        const res = await userAppApi.stats({
          ...pageParams,
          id: props.appId,
        });
        return {
          data: {
            list: res.data?.list || [],
            total: res.data?.total || 0,
          },
        };
      },
    },
  };
</script>

<style scoped></style>
