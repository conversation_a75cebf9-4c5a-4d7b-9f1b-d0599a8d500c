<template>
  <div class="function-title">使用NovaAI您可以</div>
  <div class="function-tabs">
    <div
      v-for="(item, index) in FUNCTION"
      :class="['function-tab', { active: curFunction === index }]"
      :key="index"
      @click="curFunction = index"
    >
      <span>{{ item }}</span>
    </div>
  </div>
  <div class="function-cards">
    <div
      class="function-cards-row"
      :class="{ paused: loopData.length < 3 || isPaused }"
      :style="{ animationDuration: animationDuration }"
      @mouseenter="isPaused = true"
      @mouseleave="isPaused = false"
      ref="cardsRowRef"
    >
      <template v-for="repeat in 3">
        <div
          class="function-card"
          v-for="(item, index) in loopData"
          :key="index + '-' + repeat"
          @click="handleCardClick(item.id)"
        >
          <div class="function-card-header">
            <img
              class="function-card-avatar"
              referrerpolicy="no-referrer"
              :src="item.logo"
              :alt="item.name"
              style="object-fit: contain"
            />
            <span class="function-card-title">{{ item.name }}</span>
          </div>
          <div class="function-card-desc">
            <span>{{ item.desc }}</span>
          </div>
          <div class="function-card-footer">
            <span class="function-card-extra">成交量：{{ item.star }}</span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, nextTick, computed } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  const { capabilityList } = defineProps<{
    capabilityList: {
      app_list: {
        desc: string;
        id: number;
        logo: string;
        name: string;
        star: number;
      }[];
      id: number;
      level: number;
      name: string;
    }[];
  }>();

  const FUNCTION = computed(() => {
    return capabilityList.map((item) => item.name);
  });
  const curFunction = ref(0);

  const loopData = computed(() => {
    return capabilityList[curFunction.value].app_list;
  });

  const isPaused = ref(false);

  // 动画时长，1卡片4s，最少12s
  const animationDuration = ref('12s');
  const updateAnimationDuration = () => {
    const base = 8; // 每个卡片4秒
    const min = 12; // 最小12秒
    const duration = Math.max(loopData.value.length * base, min);
    animationDuration.value = `${duration}s`;
  };
  updateAnimationDuration();

  // 监听卡片数量变化自动调整动画时长
  watch(loopData, updateAnimationDuration, { immediate: true });

  // curFunction变更时重置动画
  const cardsRowRef = ref<HTMLElement | null>(null);
  watch(curFunction, async () => {
    // 暂停动画
    isPaused.value = true;
    await nextTick();
    // 强制重绘，重置动画
    if (cardsRowRef.value) {
      // 触发重绘
      cardsRowRef.value.style.animation = 'none';
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      cardsRowRef.value.offsetHeight;
      cardsRowRef.value.style.animation = '';
    }
    // 恢复动画
    isPaused.value = false;
  });

  const handleCardClick = (id: number) => {
    router.push(`/app/detail/${id}`);
  };
</script>

<style scoped lang="less">
  .function-title {
    height: 44px;
    overflow-wrap: break-word;
    color: rgb(11 38 64 / 100%);
    font-size: 32px;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 44px;
    margin: 118px auto 0;
  }

  .function-tabs {
    display: flex;
    gap: 12px;
    margin: 48px 0 0;
    width: 980px;
    height: 40px;

    .function-tab {
      width: 112px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      cursor: pointer;
      transition: color 0.2s;
      border-radius: 8px;
      background: #fff;
      color: rgb(0 0 0 / 84%);
    }

    .function-tab.active {
      background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
      color: #fff;
    }
  }

  .function-cards {
    height: 222px;
    overflow: hidden;
    mask-image: linear-gradient(
      to right,
      transparent,
      black 10% 90%,
      transparent
    );
  }

  .function-cards-row {
    width: max-content;
    // min-width: 1588px;
    // height: 173px;
    display: flex;
    justify-content: flex-start;
    margin: 21px 0 0;
    gap: 32px;
    animation: function-cards-scroll linear infinite;

    /* 动画时长由内联style控制 */
  }

  .function-cards-row.paused {
    animation-play-state: paused;
  }

  @keyframes function-cards-scroll {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-50%);
    }
  }

  .function-card {
    width: 350px;
    min-width: 350px;
    height: 150px;
    box-shadow: 0 0 30px 0 rgb(0 0 0 / 4%);
    border-radius: 16px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 14px 32px;
    cursor: pointer;
  }

  .function-card-header {
    display: flex;
    align-items: center;
  }

  .function-card-avatar {
    border-radius: 50%;
    width: 44px;
    height: 44px;
    background-size: cover;
    background-position: center;
    margin-right: 16px;
  }

  .function-card-title {
    overflow-wrap: break-word;
    color: rgb(11 38 64 / 100%);
    font-size: 20px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 44px;
  }

  .function-card-desc {
    overflow-wrap: break-word;
    color: rgb(0 0 0 / 52%);
    font-size: 16px;
    font-weight: normal;
    text-align: left;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .function-card-footer {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .function-card-extra {
    width: 45px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgb(0 0 0 / 52%);
    font-weight: 500;
    font-size: 12px;
    white-space: nowrap;
    line-height: 24px;
  }
</style>
