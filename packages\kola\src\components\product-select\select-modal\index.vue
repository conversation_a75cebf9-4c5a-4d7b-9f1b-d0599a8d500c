<template>
  <div class="product-select">
    <header v-if="title">
      <div class="title">{{ title }}</div>
    </header>
    <div class="filter">
      <Filter v-model="filter" :form-schema="props.filter.formSchema" />
    </div>
    <OperationBar :operation="props.operation"></OperationBar>
    <div class="table">
      <DynamicTable ref="tableRef" :table="table" :auto-load="autoLoad" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, provide } from 'vue';
  import OperationBar from './operation-bar/index.vue';
  import Filter from './filter/index.vue';
  import DynamicTable from '../table/index.vue';

  import { DPage } from '../types/page';
  import { RefreshTable } from '../types/table';

  defineOptions({ name: 'DynamicPage' });

  const props = withDefaults(defineProps<DPage>(), {
    autoLoad: true,
    batchOperation: () => [],
  });

  console.log('props2', props);

  const filter = ref(props.filter.defaultFormData ?? {});

  const tableRef = ref<{ refreshTable: RefreshTable }>();

  function refreshTable(resetPagination) {
    tableRef.value?.refreshTable({ resetPagination });
  }

  provide('refresh-table', (resetPagination: boolean) => {
    refreshTable(resetPagination);
  });

  provide('filter', filter);

  provide('getCustomParams', props.getCustomParams);

  defineExpose({
    refreshTable,
  });
</script>

<style scoped lang="less">
  .dynamic-page {
    padding: 16px;
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    header {
      display: flex;
      align-items: center;

      .title {
        color: var(--color-text-1);
        font-size: 16px;
        font-weight: 500;
        margin-right: 16px;
      }
    }
  }

  .filter {
    margin-top: 16px;
  }

  .table {
    overflow: hidden;
    flex: 1;
  }
</style>
