import { h } from 'vue';
import { isString } from 'lodash';
import dayjs from 'dayjs';
import CodeToName from './code-to-name/index.vue';
import TableSwitch from './switch/index.vue';
import TableImage from './image/index.vue';
import TableEditCell from './edit-cell/index.vue';
import Operations from './operations/index.vue';
import { Column } from '../../types/table';

function resolveRender(
  column: Column,
  { refreshTable }: { refreshTable: () => void }
) {
  const { customRender, dataIndex } = column;

  const type = customRender?.type;
  const customProps = customRender?.props ?? {};

  switch (type) {
    case 'code-to-name':
      return (props) => h(CodeToName, { ...props, ...customProps });
    case 'switch':
      return (props) => h(TableSwitch, { ...props, ...customProps });
    case 'image':
      return (props) => h(TableImage, { ...props, ...customProps });
    case 'edit-cell':
      return (props) => h(TableEditCell, { ...props, ...customProps });
    case 'operations':
      return (props) => h(Operations, { ...props, ...customProps });
    case 'datetime':
      return ({ record }) =>
        dayjs(record[dataIndex]).format(
          customProps.formatter || 'YYYY-MM-DD'
        ) ?? '-';
    default:
      if (type && !isString(type)) {
        return (props) => h(type, { ...props, ...customProps, refreshTable });
      }

      return ({ record }) => record[dataIndex] ?? '-';
  }
}

export default resolveRender;
