import { http } from '@/utils/http';

interface PageReq {
  page?: number;
  limit?: number;
}

interface ConsumptionReq extends PageReq {
  app_ids?: string[];
  edt?: string;
  sdt?: string;
}

const tradeApi = {
  // 获取充值记录
  incomingList(data: PageReq) {
    return http.post('/api/v1/user/trade/incoming/list', data);
  },

  // 获取消费记录
  consumptionRecords(data: ConsumptionReq) {
    return http.post('/api/v1/user/trade/outgoing/list', data);
  },

  // 获取统计数据
  stats() {
    return http.post('/api/v1/user/trade/stats');
  },
};

export default tradeApi;
