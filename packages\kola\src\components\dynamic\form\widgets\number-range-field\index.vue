<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
    :extra="field.extra"
  >
    <a-input-number
      v-model="startValue"
      :placeholder="field.placeholder"
      :min="field.min || 0"
      :max="field.max"
      :hide-button="!!field.suffix"
      :precision="field.precision"
      :disabled="field.disabled"
      model-event="input"
      :style="field?.style"
    >
      <template #suffix v-if="field.suffix">
        {{ field.suffix }}
      </template>
    </a-input-number>
    <span style="margin: 0 8px; display: flex; align-items: center">{{
      field.separator || '-'
    }}</span>
    <a-input-number
      v-model="endValue"
      :placeholder="field.placeholder"
      :min="startValue || field.min || 0"
      :max="field.max"
      :hide-button="!!field.suffix"
      :precision="field.precision"
      :disabled="field.disabled"
      model-event="input"
      :style="field?.style"
    >
      <template #suffix v-if="field.suffix">
        {{ field.suffix }}
      </template>
    </a-input-number>
    <template #extra v-if="field.extra">
      <component :is="field.extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { ref, watch, inject, computed } from 'vue';
  import { isFunction, isNil, isNumber } from 'lodash';
  import { NumberField } from '../../../types/form';

  const formData = inject('formData');

  const props = defineProps<{
    field: NumberField;
    path: string;
  }>();

  const rangeModel = defineModel<(number | undefined)[]>({
    default: [],
    set: (val) => {
      if (props.field.onChange) {
        setTimeout(() => {
          props.field.onChange?.(val, formData);
        }, 0);
      }
      return val;
    },
  });
  if (props.field.rangeClear) {
    watch(
      () => rangeModel.value,
      (newVal) => {
        if (isNil(newVal) || newVal?.length <= 0) {
          startValue.value = undefined;
          endValue.value = undefined;
        }
      }
    );
  }

  const startValue = ref(rangeModel.value?.[0] ?? undefined);
  const endValue = ref(rangeModel.value?.[1] ?? undefined);
  watch(startValue, () => {
    rangeModel.value = [
      startValue.value ?? undefined,
      endValue.value ?? undefined,
    ];
  });

  watch(endValue, () => {
    rangeModel.value = [
      startValue.value ?? undefined,
      endValue.value ?? undefined,
    ];
  });
</script>
