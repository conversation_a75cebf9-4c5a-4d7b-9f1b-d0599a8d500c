import type { RouteRecordNormalized } from 'vue-router';
import { auth } from '@repo/sdk';

const modules = import.meta.glob('./modules/*.ts', { eager: true });

function formatModules(_modules: any, result: RouteRecordNormalized[]) {
  Object.keys(_modules).forEach((key) => {
    const defaultModule = _modules[key].default;
    if (!defaultModule) return;
    const moduleList = Array.isArray(defaultModule)
      ? [...defaultModule]
      : [defaultModule];
    result.push(...moduleList);
  });
  return auth.redirectRouter(result);
}

// 分别加载站外和站内路由
const appRoutes: RouteRecordNormalized[] = formatModules(modules, []);

export default appRoutes;
