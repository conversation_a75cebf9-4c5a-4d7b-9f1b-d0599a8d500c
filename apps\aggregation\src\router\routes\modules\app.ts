import { AppRouteRecordRaw } from '../types';
import { PUBLIC_LAYOUT } from '../base';

const APP_MARKET: AppRouteRecordRaw[] = [
  {
    path: '/app',
    name: 'app',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/app-market/index.vue'),
    },
    meta: {
      title: '应用市场',
      requiresAuth: false,
      order: 0,
    },
  },
  {
    path: '/app/detail/:id',
    name: 'appDetail',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/app-market/detail/index.vue'),
    },
    meta: {
      title: '应用详情',
      requiresAuth: false,
      order: 0,
    },
  },
];

export default APP_MARKET;
