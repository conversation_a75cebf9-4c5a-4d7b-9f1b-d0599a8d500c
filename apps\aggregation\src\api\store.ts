import { http } from '@/utils/http';

const storeAppApi = {
  // 获取应用列表
  list(data) {
    return http.post('/api/v1/store/app/list', data);
  },

  // 获取应用详情
  detail(data: { id: number }) {
    return http.post('/api/v1/store/app/info', data);
  },
  // 应用推荐
  recommend(data: { scene: 'HOT' }) {
    return http.post('/api/v1/store/app/recommend', data);
  },
  // 分类列表
  catList() {
    return http.post('/api/v1/store/cat/list');
  },
};

export default storeAppApi;
