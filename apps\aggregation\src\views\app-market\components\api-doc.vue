<template>
  <!-- API接口文档 -->
  <a-card class="api-doc" title="API接口文档" :bordered="false">
    <div class="base">
      <div class="base-title">
        调用地址：<a-link :href="accessInfo.request_endpoint" target="_blank">{{
          accessInfo.request_endpoint
        }}</a-link>
      </div>
      <div class="base-content">请求方式：{{ accessInfo.request_method }}</div>
      <div class="base-content">返回类型：{{ accessInfo.response_type }}</div>
    </div>
    <a-collapse
      v-for="item in request"
      :key="item.title"
      :default-active-key="[item.content]"
      class="collapse"
    >
      <a-collapse-item
        :header="item.title"
        class="collapse-item"
        :key="item.content"
      >
        <a-table
          :columns="columns"
          :data="accessInfo[item.content]"
          :bordered="false"
          :loading="!accessInfo[item.content]"
        />
      </a-collapse-item>
    </a-collapse>

    <a-collapse
      v-for="item in example"
      :key="item.title"
      :default-active-key="['request_demo']"
      class="collapse"
    >
      <a-collapse-item :header="item.title" :key="item.content">
        <div style="white-space: pre-wrap">{{ accessInfo[item.content] }}</div>
      </a-collapse-item>
    </a-collapse>
  </a-card>
</template>

<script setup lang="ts">
  const { accessInfo } = defineProps<{
    accessInfo: {
      request_auth_link: 'string';
      request_auth_tip: 'string';
      request_body: [
        {
          key: 'string';
          typ: 'string';
          val: 'string';
        }
      ];
      request_demo: 'string';
      request_endpoint: 'string';
      request_headers: [
        {
          key: 'string';
          typ: 'string';
          val: 'string';
        }
      ];
      request_method: 'string';
      request_path: 'string';
      request_queries: [
        {
          key: 'string';
          typ: 'string';
          val: 'string';
        }
      ];
      response_fail: 'string';
      response_params: [
        {
          key: 'string';
          typ: 'string';
          val: 'string';
        }
      ];
      response_succ: 'string';
      response_type: 'string';
    };
  }>();

  const columns = [
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: 'Type',
      dataIndex: 'typ',
      key: 'typ',
    },
    {
      title: 'Value',
      dataIndex: 'val',
      key: 'val',
    },
  ];

  const request = [
    {
      title: '请求参数（Headers）',
      content: 'request_headers',
    },
    {
      title: '请求参数（Query）',
      content: 'request_queries',
    },
    {
      title: '请求参数（Body）',
      content: 'request_body',
    },
    {
      title: '响应参数',
      content: 'response_params',
    },
  ];

  const example = [
    {
      title: '请求示例',
      content: 'request_demo',
    },
    {
      title: '正常返回示例',
      content: 'response_succ',
    },
    {
      title: '失败返回示例',
      content: 'response_fail',
    },
  ];
</script>

<style scoped lang="less">
  .api-doc {
    width: 100%;

    .base {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }

  .collapse {
    margin-top: 16px;

    // 折叠面板header和icon样式优化
    :deep(.arco-collapse-item-header) {
      background: #e7eeff;
    }

    :deep(.arco-collapse-item-header .arco-icon) {
      color: #2c6bff;
      font-size: 16px;
    }
  }

  // 折叠面板内容区样式
  :deep(.arco-collapse-item-content),
  :deep(.arco-collapse-item-content-box) {
    padding: 0;
  }

  // 表格分页器隐藏
  :deep(.arco-table-pagination) {
    display: none;
  }
</style>
