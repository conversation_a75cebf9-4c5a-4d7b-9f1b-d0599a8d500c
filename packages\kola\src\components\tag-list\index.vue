<template>
  <a-space direction="vertical" fill>
    <a-tag
      v-for="(tag, index) in tags"
      closable
      :key="tag.content ?? index"
      @close="() => handleClose(tag)"
    >
      <span class="text" :title="tag.content">{{ tag?.content }}</span></a-tag
    >
  </a-space>
</template>

<script setup lang="ts">
  const tags = defineModel<
    Array<{
      content: string;
    }>
  >('tags', { default: [] });

  const handleClose = (item) => {
    const index = tags.value.indexOf(item);
    if (index !== -1) {
      tags.value.splice(index, 1);
    }
  };
</script>

<style scoped lang="less">
  .text {
    max-width: calc(100% - 20px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
