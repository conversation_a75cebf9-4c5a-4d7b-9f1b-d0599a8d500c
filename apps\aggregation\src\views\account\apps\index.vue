<template>
  <AccountPage :filter="formSchema" :table="tableConfig" v-model="formData">
    <ConsumptionModal
      v-model:visible="consumptionModalVisible"
      :app-id="appId"
    />
  </AccountPage>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import userAppApi from '@/api/user-app';
  import router from '@/router';
  import AccountPage from '../components/account-page/index.vue';
  import ConsumptionModal from './consumption/index.vue';

  const formData = ref({
    search: '',
  });

  const consumptionModalVisible = ref(false);
  const appId = ref('');

  const formSchema = {
    fields: [
      {
        label: '应用名称',
        name: 'search',
        type: 'text',
        allowClear: true,
        span: 6,
      },
    ],
  };

  const tableConfig = {
    columns: [
      {
        title: '应用名称',
        dataIndex: 'app_name',
        width: 100,
      },
      {
        title: '应用类型',
        dataIndex: 'app_type',
        width: 130,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: 100,
        customRender: {
          type: 'operations',
          props: {
            operations: [
              {
                text: 'API接入',
                props: {
                  type: 'text',
                },
                clickActionType: 'action',
                action: ({ record }) => {
                  router.push(`/account/apps/access?app_id=${record.id}`);
                },
              },
              {
                text: '消费U点明细',
                props: {
                  type: 'text',
                },
                clickActionType: 'action',
                action: ({ record }) => {
                  appId.value = record.id;
                  consumptionModalVisible.value = true;
                },
              },
            ],
          },
        },
      },
    ],

    load: {
      action: async (_filter, pageInfo) => {
        const pageParams = {
          page: pageInfo.pageNum,
          limit: pageInfo.pageSize,
        };
        const res = await userAppApi.list({
          ...pageParams,
          ...formData.value,
        });

        return {
          data: {
            list: res.data?.list || [],
            total: res.data?.total || 0,
          },
        };
      },
    },
  };
</script>

<style scoped lang="less"></style>
