<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${compact([path, field.name]).join('.')}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <a-time-picker v-model="value" type="time-range" disable-confirm />
  </a-form-item>
</template>

<script setup lang="ts">
  import { compact } from 'lodash';
  import { TimeRangeField } from '../../../types/form';

  defineProps<{
    field: TimeRangeField;
    path: string;
  }>();

  const value = defineModel<[string, string] | undefined>({
    default: undefined,
  });
</script>
