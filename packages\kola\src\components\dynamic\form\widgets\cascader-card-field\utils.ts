import {
  cloneDeep,
  compact,
  isArray,
  isFunction,
  isNumber,
  isObject,
  unionBy,
} from 'lodash';
import { CascaderFieldNames, CascaderOption } from '@arco-design/web-vue';
import { Ref } from 'vue';
import type { CascaderOptionInfo } from './interface';
import { CascaderCardItem } from '../../../types/form';

export const getCheckedStatus = (
  option: CascaderOptionInfo,
  valueMap?: Map<string, unknown>
) => {
  let checked = false;
  let indeterminate = false;

  if (option.isLeaf) {
    if (valueMap?.has(option.key)) {
      checked = true;
    }
  } else {
    const reg = new RegExp(`^${option.key}(-|$)`);
    const checkedLeafOptionNumber = Array.from(valueMap?.keys() ?? []).reduce(
      (pre, key) => {
        if (reg.test(key)) {
          return pre + 1;
        }
        return pre;
      },
      0
    );
    if (
      checkedLeafOptionNumber > 0 &&
      checkedLeafOptionNumber >= (option.totalLeafOptions ?? 1)
    ) {
      checked = true;
    } else if (checkedLeafOptionNumber > 0) {
      indeterminate = true;
    }
  }

  return {
    checked,
    indeterminate,
  };
};

export const getRadioStatus = (
  option: CascaderOptionInfo,
  valueMap?: Map<string, unknown>
) => {
  if (option.isLeaf) {
    if (valueMap?.has(option.key)) {
      return true;
    }
  }

  const reg = new RegExp(`^${option.key}(-|$)`);
  const checkedLeafOptionNumber = Array.from(valueMap?.keys() ?? []).reduce(
    (pre, key) => {
      if (reg.test(key)) {
        return pre + 1;
      }
      return pre;
    },
    0
  );

  return checkedLeafOptionNumber > 0;
};

export const getOptionLabel = (option: CascaderOptionInfo) => {
  return option.path.map((item) => item.label).join(' / ');
};

export const getPrefixCls = (componetName: string): string => {
  return componetName;
};

export const getOptionInfos = (
  ops: CascaderOption[],
  {
    optionMap,
    leafOptionMap,
    leafOptionSet,
    leafOptionValueMap,
    totalLevel: innerLevel,
    checkStrictly,
    enabledLazyLoad,
    lazyLoadOptions,
    valueKey,
    fieldNames,
    items,
    customOptions,
  }: {
    optionMap: Map<string, CascaderOptionInfo>;
    leafOptionMap: Map<string, CascaderOptionInfo>;
    leafOptionSet: Set<CascaderOptionInfo>;
    leafOptionValueMap: Map<string, string>;
    totalLevel: Ref<number>;
    checkStrictly: Ref<boolean>;
    enabledLazyLoad: boolean;
    lazyLoadOptions: Record<string, CascaderOption[]>;
    valueKey: Ref<string>;
    fieldNames: Required<CascaderFieldNames>;
    items: CascaderCardItem[];
    customOptions: Record<string, CascaderOption[]>;
  }
) => {
  let totalLevel = 0;

  const travelOptions = (
    options: CascaderOption[],
    parent?: CascaderOptionInfo,
    level?: number
  ) => {
    const parentPath = parent?.path ?? [];
    totalLevel = Math.max(totalLevel, level ?? 1);

    return options.map((item, index) => {
      const value = item[fieldNames.value];
      const data: CascaderOptionInfo = {
        raw: item,
        // raw
        value,
        label: item[fieldNames.label] ?? String(value),
        disabled: Boolean(item[fieldNames.disabled]),
        selectionDisabled: false,
        render: item[fieldNames.render],
        tagProps: item[fieldNames.tagProps],
        isLeaf: item[fieldNames.isLeaf],
        // other
        level: parentPath.length,
        index,
        key: '',
        valueKey: String(isObject(value) ? value[valueKey.value] : value),
        parent,
        path: [],
        pathValue: [],
        isMultiple: items[parentPath.length]?.multiple ?? true,
      };
      const path = parentPath.concat(data);
      const pathValue: any[] = [];
      const key = path
        .map((v) => {
          pathValue.push(v.value);
          return v.valueKey;
        })
        .join('-');
      data.path = path;
      data.pathValue = pathValue;
      data.key = key;
      if (items.length === pathValue.length) {
        data.isLeaf = true;
      } else if (item[fieldNames.children]) {
        data.isLeaf = false;
        let curChildren = item[fieldNames.children];

        // 将新加入叶子节点也合并一下
        if (!data.isLeaf && customOptions[key]) {
          curChildren = unionBy(customOptions[key], curChildren, 'value');
        }

        data.children = travelOptions(curChildren, data, (level ?? 1) + 1);
      } else if (enabledLazyLoad && !data.isLeaf) {
        data.isLeaf = false;
        const lazyChildren = lazyLoadOptions[key];
        if (lazyChildren) {
          const curChildren = unionBy(
            customOptions[key] || [],
            lazyChildren,
            'value'
          );
          data.children = travelOptions(curChildren, data, (level ?? 1) + 1);
        }
      } else {
        data.isLeaf = true;
      }

      if (data.children && !data.disabled) {
        data.totalLeafOptions = data.children.reduce((pre, v) => {
          if (isNumber(v.totalLeafOptions)) {
            return pre + v.totalLeafOptions;
          }

          if (v.disabled || v.selectionDisabled) {
            return pre;
          }

          return pre + (v.isLeaf ? 1 : 0);
        }, 0);

        if (data.totalLeafOptions === 0 && !checkStrictly.value) {
          data.selectionDisabled = true;
        }
      }

      optionMap.set(data.key, data);
      if (data.isLeaf || checkStrictly.value) {
        leafOptionSet.add(data);
        leafOptionMap.set(data.key, data);
        if (!leafOptionValueMap.has(data.valueKey)) {
          leafOptionValueMap.set(data.valueKey, data.key);
        }
      }
      return data;
    });
  };

  const result = travelOptions(ops);
  innerLevel.value = totalLevel;
  return result;
};

export const getValueKey = (
  value: any,
  {
    valueKey,
    leafOptionValueMap,
  }: { valueKey: string; leafOptionValueMap: Map<string, string> }
): string => {
  if (isArray(value)) {
    return value
      .map((item) => {
        if (isObject(item)) return item[valueKey];
        return item;
      })
      .join('-');
  }
  const v = isObject(value) ? value[valueKey] : value;
  return leafOptionValueMap.get(String(v)) ?? String(v);
};

export const getLeafOptionKeys = (option: CascaderOptionInfo) => {
  const keys: string[] = [];
  if (option.isLeaf) {
    keys.push(option.key);
  } else if (option.children) {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of option.children) {
      keys.push(...getLeafOptionKeys(item));
    }
  }
  return keys;
};

export const getLeafOptionInfos = (option: CascaderOptionInfo) => {
  const infos: CascaderOptionInfo[] = [];
  if (option.disabled || option.selectionDisabled) {
    return infos;
  }
  if (option.isLeaf) {
    infos.push(option);
  } else if (option.children) {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of option.children) {
      infos.push(...getLeafOptionInfos(item));
    }
  }
  return infos;
};

export function selectedPathToOption() {}

/**
 * 获取当前节点的第一级父节点
 * @param node
 */
export function getFirstLevelParent(node: CascaderOptionInfo) {
  if (node.level === 0) {
    return node;
  }
  if (!node.parent) {
    return node;
  }
  return getFirstLevelParent(node.parent);
}

/**
 * 结果规整
 * @param resultMap
 * @param optionsInfoMap
 */
export function formatResultLabel(
  resultMap: Map<string, string>,
  optionsInfoMap: Map<string, CascaderOptionInfo>
) {
  const returnMap: Map<string, CascaderOptionInfo[]> = new Map();

  resultMap?.forEach((value, key) => {
    const resultNode = optionsInfoMap.get(key);
    if (!resultNode) {
      return;
    }
    const { parent } = resultNode;
    const parentPath = parent?.path?.map((v) => v.label).join('-') || '';

    if (returnMap.has(parentPath)) {
      returnMap.get(parentPath)?.push(resultNode);
    } else {
      returnMap.set(parentPath, [resultNode]);
    }
  });

  return returnMap;
}

export function searchOptions(
  searchValue: string,
  options: CascaderOptionInfo[],
  isSearchValue?: boolean
) {
  const result: CascaderOptionInfo[] = [];
  const reg = new RegExp(searchValue, 'i');
  const travel = (option: CascaderOptionInfo) => {
    const flag = option.path.some((v) => {
      return isSearchValue
        ? reg.test(`${v.label}${v.value}`)
        : reg.test(v.label);
    });
    if (flag) {
      result.push(option);
    }
  };
  // eslint-disable-next-line no-restricted-syntax
  for (const item of options) {
    travel(item);
  }
  return result;
}

// 树结构的数据转换
export function convertTreeData(data: CascaderOption[]) {
  const keyList: string[] = [];

  function travel(item: CascaderOption, parentKey: string) {
    const curKey = parentKey
      ? `${parentKey}-${item.value}`
      : (item.value as string);
    if (item.children?.length) {
      item.children.forEach((child) => {
        travel(child, curKey);
      });
    } else {
      keyList.push(curKey);
    }
  }

  if (Array.isArray(data)) {
    data.forEach((item) => {
      travel(item, '');
    });
  }

  return keyList;
}

// 将结果转换为树结构
export function valueToTree(
  values: string[],
  infos: Map<any, CascaderOptionInfo>,
  getMapField?: (info: any) => any
) {
  const result: CascaderOption[] = [];
  values.forEach((value) => {
    const itemInfoList: CascaderOption[] = [];
    let itemInfo = infos.get(value);
    while (itemInfo) {
      const data: any = {
        label: itemInfo.label,
        value: itemInfo.value,
      };
      const mapField = getMapField?.(itemInfo);
      if (mapField) {
        Object.assign(data, mapField);
      }
      itemInfoList.push(data);
      itemInfo = itemInfo.parent;
    }
    itemInfoList.reverse();

    treePushNodeList(result, itemInfoList);
  });

  return result;
}

function treePushNodeList(tree: CascaderOption[], nodeList: CascaderOption[]) {
  nodeList.reduce((pre, cur) => {
    const item = pre.find((v) => v.value === cur.value) as CascaderOption;
    if (item) {
      return item.children as CascaderOption[];
    }
    const node: any = {
      ...cur,
      label: cur.label,
      value: cur.value,
      children: [] as CascaderOption[],
    };
    pre.push(node);
    return node.children;
  }, tree);
}

export function getInitValueSelectPath(value: CascaderOption[]) {
  const result: string[] = [];
  if (!value.length) {
    return result;
  }

  let curItem: CascaderOption | undefined = value[0];
  let curPath = '';
  while (curItem) {
    curPath = compact([curPath, curItem.value]).join('-');
    result.push(curPath);
    curItem = curItem.children?.[0];
  }

  return result;
}
