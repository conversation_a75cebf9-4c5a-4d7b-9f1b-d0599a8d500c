<template>
  <a-form-item
    :label="field.label"
    :field="compact([path, field.name]).join('.')"
    :content-flex="field.layout === 'inline'"
    :wrapper-col-style="{ marginBottom: 0 }"
    :rules="field.rules"
    :hide-label="hideLabel"
    class="object-wrapper"
  >
    <div v-if="field.extra">
      <component :is="field.extra"></component>
    </div>
    <UpRenderCom
      v-if="isUpRender"
      v-model="value"
      :field="props.field"
      :path="props.path"
    >
    </UpRenderCom>
    <FieldRender
      v-else
      v-model="value"
      :field="props.field"
      :path="props.path"
    ></FieldRender>
    <template #extra v-if="field.footerExtra">
      <component :is="field.footerExtra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { compact } from 'lodash';
  import { computed, inject } from 'vue';
  import { ObjectField } from '../../../types/form';
  import FieldRender from './component/field-render.vue';
  import UpRenderCom from './component/up-render.vue';

  const value = defineModel<Record<string, any>>({ default: {} });
  const props = defineProps<{
    field: ObjectField;
    path: string;
    upRender?: boolean;
  }>();

  const formUpRender = inject('formUpRender', false);
  const isUpRender = computed(() => {
    return props.upRender || formUpRender;
  });

  const viewType = computed(() => {
    return {
      isCard: props.field.viewType === 'card',
    };
  });
  const hideLabel = computed(() => {
    if (viewType.value.isCard) {
      return true;
    }
    return props.field.hideLabel;
  });
</script>

<style scoped>
  .object-wrapper {
    margin-bottom: 0;
  }
</style>
