<template>
  <section class="app-detail">
    <div class="header">
      <div class="header_container">
        <div class="header-left">
          <div class="header-left-title">
            <span class="header-left-title-text">{{ caseInfo.name }}</span>
          </div>
          <div class="desc">
            <span class="desc-text">
              {{ caseInfo.desc }}
            </span>
          </div>
        </div>
        <img
          style="width: 350px; height: 200px"
          :src="caseInfo.cover"
          alt="header-right"
        />
      </div>
    </div>
    <div class="container">
      <a-breadcrumb style="margin: 24px 0">
        <template #separator>
          <icon-right />
        </template>
        <a-breadcrumb-item
          v-for="(item, index) in breadcrumb"
          :key="item"
          @click="handleBreadcrumbClick(item, index)"
          >{{ item }}</a-breadcrumb-item
        >
      </a-breadcrumb>
      <div class="content">
        <!-- 展示富文本 -->
        <div class="content-left" v-html="caseInfo.rich"></div>
        <!-- <div class="content-right"> </div> -->
      </div>
      <div class="footer">
        <div class="title">
          <span class="title-text">更多成功案例</span>
        </div>
        <CaseCard
          style="margin-bottom: 60px"
          card-style="height: 318px"
          :case-list="caseInfo.recommend_case_list"
        />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import CaseCard from '@/components/common/CaseCard.vue';
  import { getCaseInfo } from '@/api/case';

  const router = useRouter();

  const breadcrumb = ref(['首页', '全部', 'XXX案例']);
  const handleBreadcrumbClick = (item: string, index: number) => {
    if (index === 0) {
      router.push(`/home`);
    } else if (index === 1) {
      router.push(`/case`);
    } else {
      router.push(`/case?industry=${caseInfo.value.industry}`);
    }
  };

  const caseInfo = ref({
    cover: 'string',
    desc: 'string',
    id: 0,
    industry: 'string',
    name: 'string',
    recommend_case_list: [
      {
        cover: 'string',
        desc: 'string',
        id: 0,
        name: 'string',
      },
    ],
    rich: 'string',
  });

  const getCaseInfoData = async () => {
    const { id } = router.currentRoute.value.params;
    const res = await getCaseInfo({ id: Number(id) });
    caseInfo.value = res.data;
    breadcrumb.value[2] = caseInfo.value.industry;
  };

  onMounted(() => {
    getCaseInfoData();
  });
</script>

<style scoped lang="less">
  .app-detail {
    width: 100%;
    height: 100%;
  }

  .header {
    width: 100%;
    height: 232px;
    background: #ebf4ff;

    .header_container {
      max-width: 1440px;
      width: 100%;
      padding: 0 40px;
      height: 100%;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        flex: 1;
        padding-right: 32px;
      }

      span {
        height: 28px;
        font-weight: 500;
        font-size: 24px;
        color: rgb(0 0 0 / 84%);
        line-height: 28px;
      }

      .desc {
        margin-top: 16px;

        span {
          font-size: 16px;
          color: rgb(0 0 0 / 84%);
          line-height: 28px;
        }
      }
    }
  }

  .container {
    max-width: 1440px;
    width: 100%;
    padding: 0 40px;
    display: flex;
    flex-direction: column;
    margin: 0 auto;

    .content {
      display: flex;
      gap: 22px;
      width: 100%;

      .content-left {
        flex: 1;
        padding: 26px;
      }

      .content-right {
        width: 264px;
      }
    }

    .content > div {
      box-shadow: 0 2px 4px 0 rgb(37 48 68 / 4%),
        0 6px 12px 0 rgb(37 48 68 / 12%);
      border-radius: 8px;
      background: #fff;
    }

    .footer {
      .title {
        margin: 24px 0;

        span {
          height: 28px;
          font-weight: 500;
          font-size: 20px;
          color: rgb(0 0 0 / 84%);
          line-height: 28px;
        }
      }
    }
  }

  .card {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .avatar {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgb(0 0 0 / 3%);
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #222;
      }
    }

    .description {
      margin-top: 15px;
      margin-bottom: 24px;
      font-size: 14px;
      color: #666;
      line-height: 1.7;
      min-height: 40px;
    }

    .card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 986px;
      height: 88px;
      background: #e7eeff;
      border-radius: 8px;
      padding: 0 32px;

      .price {
        font-weight: 500;

        .price-text {
          font-size: 28px;
          color: #ff9100;
          margin-right: 4px;
          line-height: 40px;
        }

        .price-unit {
          line-height: 14px;
          font-size: 10px;
          color: rgb(0 0 0 / 84%);
        }
      }

      .detail-btn {
        font-size: 12px;
        color: rgb(11 38 64 / 68%);
      }
    }

    .read-agreement {
      margin: 24px 0;
      height: 20px;
      font-size: 14px;
      color: rgb(0 0 0 / 40%);
      line-height: 20px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .create-btn {
      width: 120px;
      height: 40px;
      background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
      border-radius: 8px;
      font-weight: 500;
      font-size: 16px;
      color: #fff;
    }
  }
</style>
