<template>
  <div style="flex: auto; overflow: hidden">
    <a-scrollbar style="height: 459px; overflow: auto">
      <div class="card-section">
        <div v-for="key in labelList" :key="key[0]">
          <div class="label-title">
            <a-tooltip :content="key[0]" position="bottom">
              <div class="label-title-text">{{ key[0] }}</div>
            </a-tooltip>
            <a-button
              v-if="field.isReverse ?? true"
              size="mini"
              type="text"
              @click="
                () => {
                  cascaderCtx?.reverseSelect?.(key[1]);
                }
              "
              >反选
            </a-button>
          </div>
          <a-tag
            size="large"
            v-for="item in key[1]"
            :key="item.key"
            class="label-item"
            :class="{
              'label-item-show-value': field.isShowValue,
            }"
            closable
            @close="() => cascaderCtx?.onClickOption?.(item, false)"
          >
            <a-tooltip
              :content="item.label"
              position="bottom"
              :disabled="item.label.length < 10"
            >
              <template #content>
                <div class="tooltip-content">
                  <p>{{ item.label }}</p>
                  <p v-if="props.field.isShowValue">{{ item.value }}</p>
                </div>
              </template>
              <div class="select-box">
                <div class="label-title-text">{{ item.label }}</div>
                <div
                  class="label-title-text label-title-value"
                  v-if="props.field.isShowValue"
                  >{{ item.value }}</div
                >
              </div>
            </a-tooltip>
          </a-tag>
        </div>
      </div>
    </a-scrollbar>
  </div>
</template>

<script setup lang="ts">
  import { inject, computed } from 'vue';
  import { CascaderContext, cascaderInjectionKey } from './context';
  import { formatResultLabel } from './utils';
  import { CascaderCardField, FormData } from '../../../types/form';

  const props = defineProps<{
    field: CascaderCardField;
    // path: string;
  }>();

  const cascaderCtx = inject<Partial<CascaderContext>>(
    cascaderInjectionKey,
    {}
  );

  const labelList = computed(() => {
    return formatResultLabel(cascaderCtx.valueMap!, cascaderCtx.optionMap!);
  });
</script>

<style lang="less" scoped>
  .card-section {
    padding: 0 8px 8px;
  }

  .label-title {
    padding: 10px 0;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .label-title-text {
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .label-title-value {
    font-size: 13px;
  }

  .label-item {
    width: 100%;
    //padding: 16px 8px;
    font-weight: normal;
    margin-bottom: 4px;
  }

  .label-item.label-item-show-value {
    height: auto;

    .select-box {
      width: 90%;
      line-height: 25px;
    }
  }

  :deep(.arco-tag) {
    display: flex;
    justify-content: space-between;
  }

  :deep(.arco-btn-size-mini) {
    padding: 0 8px;
  }

  .tooltip-content {
    p {
      margin: 0;
    }
  }
</style>
