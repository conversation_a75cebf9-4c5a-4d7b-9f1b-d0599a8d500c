<template>
  <AccountPage :filter="formSchema" :table="tableConfig" v-model="formData">
  </AccountPage>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import dayjs from 'dayjs';
  import { omit } from 'lodash';
  import tradeApi from '@/api/trade';
  import userKeyApi from '@/api/user-key';
  import AccountPage from '../components/account-page/index.vue';

  const formData = ref({
    edt: '',
    sdt: '',
    key_ids: [],
  });

  const formSchema = {
    fields: [
      {
        label: '选择时间',
        name: 'date',
        type: 'dateRange',
        allowClear: true,
        span: 9,
        disabledDate: (current) => {
          return current && current > dayjs();
        },
        onChange: (value, form) => {
          form.value.edt = value?.[1] || '';
          form.value.sdt = value?.[0] || '';
        },
      },
      {
        label: 'APIKey',
        name: 'key_ids',
        span: 8,
        type: 'select',
        format: 'multipleSelect',
        select: {
          allowClear: true,
          tooltip: true,
          maxTagCount: 1,
        },
        source: {
          labelKey: 'name',
          valueKey: 'id',
          data: async () => {
            const res = await userKeyApi.list({
              limit: 10000,
              page: 1,
            });

            return res?.data?.list || [];
          },
        },
      },
    ],
  };

  const tableConfig = {
    columns: [
      {
        title: 'APIKey名称',
        dataIndex: 'key_name',
        width: 100,
      },
      {
        title: '消耗时间',
        dataIndex: 'task_time',
        width: 100,
      },
      {
        title: '消耗类型',
        dataIndex: 'price_typ',
        width: 100,
      },
      {
        title: '任务详情',
        dataIndex: 'task_action',
        width: 100,
      },
      {
        title: '模型',
        dataIndex: 'task_model',
        width: 100,
      },
      {
        title: '消费U点',
        dataIndex: 'price_val',
        width: 100,
      },
      {
        title: '任务ID',
        dataIndex: 'task_id',
        width: 100,
      },
    ],

    load: {
      action: async (_filter, pageInfo) => {
        const pageParams = {
          page: pageInfo.pageNum,
          limit: pageInfo.pageSize,
        };
        const res = await tradeApi.consumptionRecords({
          ...pageParams,
          ...omit(formData.value, ['date']),
        });

        return {
          data: {
            list: res.data?.list || [],
            total: res.data?.total || 0,
          },
        };
      },
    },
  };
</script>

<style scoped lang="less"></style>
