<template>
  <a-modal
    v-model:visible="visible"
    :footer="false"
    hide-title
    :modal-style="{
      width: 'auto',
      background: 'unset',
    }"
    unmount-on-close
  >
    <gocaptcha-slide
      :key="captchaData.captKey"
      :data="captchaData"
      :config="captchaConfig"
      :events="{
        close: captchaClose,
        refresh: captchaRefresh,
        confirm: captchaConfirm,
      }"
    />
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import commonApi from '@/api/common';

  const visible = defineModel<boolean>();
  const emit = defineEmits(['confirm']);

  const captchaData = ref({
    captKey: '',
    image: '',
    thumb: '',
    thumbX: 0,
    thumbY: 0,
    thumbWidth: 0,
    thumbHeight: 0,
  });

  const captchaConfig = {
    title: '请拖动滑块进行验证',
  };

  /**
   * 获取验证码图片
   */
  const getVerificationCode = () => {
    commonApi.getCaptcha().then((result: any) => {
      const { data } = result;
      captchaData.value.image = data.image_base64 || '';
      captchaData.value.thumb = data.tile_base64 || '';
      captchaData.value.captKey = data.captcha_key || '';
      captchaData.value.thumbX = data.tile_x || 0;
      captchaData.value.thumbY = data.tile_y || 0;
      captchaData.value.thumbWidth = data.tile_width || 0;
      captchaData.value.thumbHeight = data.tile_height || 0;
    });
  };

  const captchaClose = () => {
    visible.value = false;
  };

  const captchaRefresh = () => {
    getVerificationCode();
  };

  const captchaConfirm = (data) => {
    emit('confirm', {
      captcha_point: `${data.x},${data.y}`,
      captcha_key: captchaData.value.captKey,
    });
  };

  defineExpose({
    captchaRefresh,
  });

  watch(visible, () => {
    if (visible.value) {
      getVerificationCode();
    }
  });
</script>

<style scoped lang="less"></style>
