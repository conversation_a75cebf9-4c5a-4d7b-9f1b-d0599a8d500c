<template>
  <a-spin :loading="loading" class="group-select">
    <div v-if="title" class="group-title">
      <h4>{{ title }}</h4>
      <a-button
        type="primary"
        @click="handleAction('create')"
        v-auth="props.modal?.create?.auth"
        >新增
      </a-button>
    </div>
    <a-input placeholder="输入关键字进行过滤" v-model="searchKey" allow-clear />
    <ul class="group-list" v-show="cloneList.length > 0">
      <li
        v-for="item in cloneList"
        :key="item[id]"
        class="group-item"
        :class="activeId === item[id] ? 'active' : ''"
        @click="handleItemClick(item)"
      >
        <div class="group-left">
          <p class="group-left-label">{{ item[label] }}</p>
          <p
            v-if="item[code]"
            class="group-left-code"
            @click.stop="handleCodeClick(item)"
          >
            {{ item[code] }}
          </p>
        </div>
        <div class="group-right">
          <icon-copy
            v-if="
              modal.copy !== undefined &&
              (!modal.copy.disable || !modal.copy.disable(item))
            "
            v-auth="props.modal?.copy?.auth"
            class="action-icon"
            @click.stop="handleAction('copy', item)"
          />
          <icon-edit
            class="action-icon"
            v-if="
              modal.modify !== undefined &&
              (!modal.modify.disable || !modal.modify.disable(item))
            "
            v-auth="props.modal?.modify?.auth"
            @click.stop="handleAction('modify', item)"
          />
          <a-popconfirm
            content="确定删除该分组吗"
            v-if="
              remove !== undefined && (!remove.disable || !remove.disable(item))
            "
            type="error"
            :ok-loading="deleteLoading"
            :on-before-ok="(done) => deleteGroup(done, item)"
          >
            <icon-delete
              v-auth="props.remove?.auth"
              class="action-icon"
              @click.stop
            />
          </a-popconfirm>
        </div>
      </li>
    </ul>
    <a-empty v-show="cloneList.length <= 0" />
    <GroupModal
      v-model="modalParams.visible"
      v-bind="modalParams"
      @success="fetchData"
    />
  </a-spin>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import GroupModal from '@/components/group-select/group-modal/index.vue';
  import { copyText } from '@/utils';
  import useLoading from '@/hooks/loading';

  // 接收父组件参数并设置默认值
  interface TreeFilterProps {
    load: {
      action: (data?: any) => Promise<any>;
    };
    remove?: {
      auth?: string | string[];
      action: (data?: any) => Promise<any>;
      disable?: (record: any) => boolean;
    };
    modal: {
      create?: {
        title: string;
        auth?: string | string[];
        disable?: (record: any) => boolean;
        action: (data: any) => Promise<any>;
      };
      modify?: {
        title: string;
        auth?: string | string[];
        disable?: (record: any) => boolean;
        action: (data: any) => Promise<any>;
      };
      copy?: {
        title: string;
        auth?: string | string[];
        disable?: (record: any) => boolean;
        action: (data: any) => Promise<any>;
      };
    };
    title?: string; //  标题 ==> 非必传
    id?: string; // 选择的id ==> 非必传，默认为 “id”
    label?: string; // 显示的label ==> 非必传，默认为 “name”
    code?: string; // 显示的code ==> 非必传，默认为 “code”
    defaultValue?: any; // 默认选中的值 ==> 非必传
    autoLoad?: boolean; // 是否自动就请求
    formSchema: any;
    formData: object;
  }

  const props = withDefaults(defineProps<TreeFilterProps>(), {
    id: 'id',
    label: 'name',
    code: 'code',
    autoLoad: true,
  });
  const emits = defineEmits(['change']);
  const listData = ref<any>([]);
  const activeId = ref<string | number>('');
  const { loading, setLoading } = useLoading();
  const searchKey = ref('');

  const cloneList = computed(() => {
    if (searchKey.value) {
      return listData.value.filter((item) =>
        item[props.label].includes(searchKey.value)
      );
    }

    return listData.value;
  });

  function setSelect() {
    if (listData.value.length > 0) {
      const firstOptionId = listData.value[0][props.id];
      const activeItem = listData.value.find(
        (item) => item[props.id] === activeId.value
      );
      if (!activeItem) {
        activeId.value = firstOptionId;
        emits('change', activeId.value, listData.value[0]);
      } else {
        emits('change', activeId.value, activeItem);
      }
    }
  }

  async function fetchData() {
    setLoading(true);
    try {
      const res = await props.load.action();
      listData.value = res.data;
      setSelect();
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('err', e);
    } finally {
      setLoading(false);
    }
  }

  function initGroups() {
    if (props.autoLoad) {
      if (props.defaultValue) {
        activeId.value = props.defaultValue;
      }
      fetchData();
    }
  }

  initGroups();

  function handleItemClick(item) {
    const itemId = item[props.id];
    if (itemId === activeId.value) {
      return;
    }
    activeId.value = itemId;
    emits('change', itemId, item);
  }

  function handleCodeClick(item) {
    copyText(item[props.code]);
  }

  const modalParams = ref({
    visible: false,
    type: '',
    row: {},
    formSchema: {
      fields: [],
    },
    formData: {},
    operation: {},
  });

  function handleAction(type: string, item?: any) {
    const { formSchema, formData, modal } = props;
    modalParams.value = {
      visible: true,
      type,
      row: type === 'create' ? {} : item,
      formSchema,
      formData,
      operation: modal[type],
    };
  }

  const deleteLoading = ref(false);

  function deleteGroup(done, item) {
    props.remove
      ?.action(item)
      .then(() => {
        fetchData();
        Message.success('操作成功');
        done(true);
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.log('err', err);
        done(false);
      });
  }
</script>

<style scoped lang="less">
  .group-select {
    background-color: #fff;

    p {
      margin: 0;
    }

    height: 100%;
    width: 300px;
    padding: 10px 8px;
    border: 1px solid var(--color-border);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    flex: none;

    .group-title {
      margin: 0 0 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
        color: var(--color-text-1);
        letter-spacing: 0.5px;
      }
    }

    .group-list {
      list-style: none;
      flex: 1;
      padding: 0;
      margin: 0;
      overflow-y: auto;
      height: 100%;

      .group-item {
        display: flex;
        border-radius: 5px;
        margin: 10px 0;
        align-items: center;
        color: var(--color-text-2);

        &:hover {
          background-color: var(--color-secondary-hover);
        }

        &.active {
          background-color: rgb(var(--primary-6));
          color: #fff;
        }

        .group-left {
          flex: 1;

          .group-left-label {
            word-break: break-all;
            padding: 10px;
            cursor: pointer;
          }

          .group-left-code {
            word-break: break-all;
            user-select: none;
            padding-left: 10px;
            padding-bottom: 10px;
            cursor: pointer;
            font-size: 12px;
          }
        }

        .group-right {
          margin-left: 10px;
          padding-right: 10px;

          .action-icon {
            cursor: pointer;
            margin-left: 8px;
            font-size: 18px;

            &:first-child {
              margin-left: 0;
            }
          }
        }
      }
    }
  }
</style>
