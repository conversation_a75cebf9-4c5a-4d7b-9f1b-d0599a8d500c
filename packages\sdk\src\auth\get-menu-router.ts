import { cloneDeep, isNil } from 'lodash';
import { ServerMenuItem } from './types';

/**
 * 数据展开
 * @param menuList
 * @param featureSwitch
 * @param topUniqueCode
 */
export function getFlatTreeList(
  menuList: any,
  featureSwitch?: Record<string, boolean>,
  topUniqueCode: string | null = null
) {
  const newMenuList = cloneDeep(menuList);
  return newMenuList
    .flatMap((item) => {
      const currentUniqueCode = item.uniqueCode; // 获取当前节点的 uniqueCode

      // 如果是顶级节点，更新 topUniqueCode
      const code = topUniqueCode || currentUniqueCode;
      item.topRouteName = code;
      return [
        item,
        ...(Array.isArray(item.children) && item.children.length
          ? getFlatTreeList(item.children, featureSwitch, code)
          : []),
      ];
    })
    .filter((v) => {
      if (!featureSwitch) {
        return true;
      }
      return !featureSwitch[v.name];
    });
}

export function redirectRouter(routerList) {
  routerList.forEach((item) => {
    if (
      (isNil(item.components) &&
        isNil(item.component) &&
        isNil(item.redirect)) ||
      item.component?.name === 'DEFAULT_LAYOUT'
    ) {
      item.redirect = '/not-found';
    }
    if (Array.isArray(item.children) && item.children.length) {
      redirectRouter(item.children);
    }
  });
  return routerList;
}

/**
 * 服务端menu和客户端合并
 * @param localFlatList
 * @param dynamicList
 */
function serverToLocalMenu(localFlatList, dynamicList: ServerMenuItem[]) {
  const result: any = [];
  // eslint-disable-next-line consistent-return
  dynamicList.forEach((serverRoute: any) => {
    let currentRoute: any = null;
    const localRole = localFlatList.find(
      (fItem) => fItem.name === serverRoute.uniqueCode
    );
    if (localRole) {
      currentRoute = {
        path: localRole.path,
        name: localRole.name,
        component: localRole.components ?? localRole.component,
        meta: {
          ...(localRole.meta ?? {}),
          title: serverRoute.menuName,
        },
      };
      result.push(currentRoute);
    }
    const isChildren =
      Array.isArray(serverRoute.children) && serverRoute.children.length;

    if (isChildren && serverRoute.type !== 3) {
      const childRouter = serverToLocalMenu(
        localFlatList,
        serverRoute.children
      );
      if (currentRoute) {
        currentRoute.children =
          Array.isArray(childRouter) && childRouter.length > 0
            ? childRouter
            : undefined;
      }
    }
  });
  return result;
}

/**
 * 服务端路由的path和对应的按钮列表
 * @param localFlatList
 * @param menuList
 */
function getButtonsAndPath(localFlatList, menuList: ServerMenuItem[]) {
  const flatList = getFlatTreeList(menuList);
  const buttons: any = [];
  // @ts-ignore
  const topNameMap = new Map();
  let firstPath = '';

  flatList.forEach((backRoute: ServerMenuItem) => {
    if (backRoute.type === 3 && backRoute.uniqueCode) {
      buttons.push(backRoute.uniqueCode);
    }
    if (firstPath === '') {
      if (
        Array.isArray(backRoute.buttonTypeCodes) &&
        backRoute.buttonTypeCodes.length
      ) {
        const localRote = localFlatList.find(
          (fItem) => fItem.name === backRoute.uniqueCode
        );
        if (localRote) {
          firstPath = localRote.path;
        }
      }
    }
    topNameMap.set(backRoute.uniqueCode, backRoute.topRouteName);
  });
  return {
    buttons,
    firstPath,
    flatMenuList: flatList,
    topNameMap,
  };
}

// eslint-disable-next-line no-shadow
function addWhiteRouter(localList, diffRouter, buttons, topNameMap) {
  localList.forEach((localRoute) => {
    const bindButton = localRoute.meta?.bindButton || '';

    let hasButtonPerspiration = false;

    let hasButtonCode = '';
    if (typeof bindButton === 'string') {
      hasButtonPerspiration = buttons.includes(localRoute.meta?.bindButton);
      if (hasButtonPerspiration) {
        hasButtonCode = localRoute.meta?.bindButton;
      }
    } else if (Array.isArray(bindButton)) {
      hasButtonPerspiration = bindButton.some((code) => {
        const isHasButton = buttons.includes(code);
        if (isHasButton) {
          hasButtonCode = code;
        }
        return isHasButton;
      });
    }
    if (localRoute.meta?.isWhite) {
      diffRouter.push(localRoute);
    }
    if (hasButtonPerspiration) {
      delete localRoute.children;
      diffRouter.push(localRoute);
    }
    if (hasButtonCode) {
      // 根据本地路由名称设置topRouteName
      topNameMap.set(localRoute.name, topNameMap.get(hasButtonCode));
    }
    if (Array.isArray(localRoute.children) && localRoute.children.length) {
      addWhiteRouter(localRoute.children, diffRouter, buttons, topNameMap);
    }
  });

  return diffRouter;
}
export function getMenuRouter({
  localRoute,
  menuList,
  featureSwitch,
}: {
  localRoute: any;
  menuList: ServerMenuItem[];
  featureSwitch?: Record<string, boolean>;
}) {
  const localRouterList = getFlatTreeList(localRoute, featureSwitch);

  const { buttons, firstPath, flatMenuList, topNameMap } = getButtonsAndPath(
    localRouterList,
    menuList
  );

  return {
    router: addWhiteRouter(
      localRoute,
      serverToLocalMenu(localRouterList, menuList),
      buttons,
      topNameMap
    ),
    buttons,
    firstPath,
    flatMenuList,
    topNameMap,
  };
}

export default null;
