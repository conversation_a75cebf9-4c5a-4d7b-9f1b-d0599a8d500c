<template>
  <section class="app-market">
    <!-- 搜索框 -->
    <div class="search-bar">
      <input
        v-model="filterData.search"
        class="search-input"
        type="text"
        placeholder="搜索：您感兴趣的应用"
      />
      <button class="search-btn" aria-label="搜索" @click="getUserAppListData">
        <svg
          width="16"
          height="16"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <circle cx="11" cy="11" r="7"></circle>
          <line x1="16.65" y1="16.65" x2="21" y2="21"></line>
        </svg>
      </button>
    </div>
    <div class="content">
      <div class="content-left">
        <a-menu
          v-if="sceneCategory.length > 0 && abilityCategory.length > 0"
          :style="{ width: '200px', height: '100%' }"
          :auto-scroll-into-view="true"
          :auto-open-selected="true"
          v-model:selected-keys="selectedKeys"
          :default-open-keys="selectedKeys"
        >
          <a-menu-item-group
            title="场景分类"
            style="cursor: pointer"
            @click="handleGroupClick(1)"
          >
          </a-menu-item-group>
          <a-sub-menu v-for="item in sceneCategory" :key="item.id">
            <template #title>{{ item.name }}</template>
            <a-menu-item
              v-for="child in item.child"
              :key="child.id"
              @click="handleSubMenuClick(child.id, CAT_TYPE[2][1])"
            >
              {{ child.name }}
            </a-menu-item>
          </a-sub-menu>
          <a-menu-item-group
            title="能力分类"
            style="margin-top: 16px; cursor: pointer"
            @click="handleGroupClick(0)"
          >
          </a-menu-item-group>
          <a-sub-menu v-for="item in abilityCategory" :key="item.id">
            <template #title>{{ item.name }}</template>
            <a-menu-item
              v-for="child in item.child"
              :key="child.id"
              @click="handleSubMenuClick(child.id, CAT_TYPE[2][0])"
            >
              {{ child.name }}
            </a-menu-item>
          </a-sub-menu>
        </a-menu>
      </div>
      <a-divider direction="vertical" />
      <Table
        :table-data="tableData"
        :total="total"
        v-model:filter-data="filterData"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { debounce } from 'lodash';
  import storeAppApi from '@/api/store';
  import Table from './components/table.vue';

  const route = useRoute();
  const router = useRouter();

  // 一二级文类 && 场景、能力分类
  const CAT_TYPE = {
    '1': ['CAPABILITY_FIRST_CAT', 'SCENE_FIRST_CAT'],
    '2': ['CAPABILITY_SECOND_CAT', 'SCENE_SECOND_CAT'],
  };

  const filterData = ref<{
    search: string;
    qryCatIds: number | undefined;
    sortField: 'sales';
    page: number;
    limit: number;
    qryCatTyp: string;
    sortType: 'desc' | 'asc';
  }>({
    search: '',
    page: 1,
    limit: 15,
    qryCatTyp: '',
    qryCatIds: undefined,
    sortField: 'sales', // 写死
    sortType: 'desc',
  });
  const selectedKeys = ref<number[]>([]);

  // 二级分类点击
  function handleSubMenuClick(qryCatId: number, qryCatTyp: string) {
    filterData.value.qryCatIds = qryCatId;
    filterData.value.qryCatTyp = qryCatTyp;
    router.push({
      query: {
        id: qryCatId,
        level: 2,
      },
    });
  }

  function handleGroupClick(index) {
    filterData.value.qryCatIds = undefined;
    filterData.value.qryCatTyp = CAT_TYPE[1][index];
    selectedKeys.value = [];
    router.push({
      query: {
        id: undefined,
        level: 1,
      },
    });
  }

  const tableData = ref([]);
  const total = ref(0);

  const getUserAppListData = async () => {
    const { qryCatIds, qryCatTyp, search, sortField, page, limit, sortType } =
      filterData.value;
    const res = await storeAppApi.list({
      limit,
      page,
      qry_cat_ids: [qryCatIds],
      qry_cat_typ: qryCatTyp,
      search,
      sort_field: sortField,
      sort_type: sortType,
    });
    tableData.value = res.data.list;
    total.value = res.data.total;
  };

  type catType = {
    child: {
      id: number;
      name: string;
    }[];
    id: number;
    level: number;
    name: string;
  };
  const sceneCategory = ref<catType[]>([]);
  const abilityCategory = ref<catType[]>([]);
  const getCatListData = async () => {
    const res = await storeAppApi.catList();
    sceneCategory.value = res.data.scene_list;
    abilityCategory.value = res.data.capability_list;
  };

  getCatListData();

  watch(
    filterData,
    // 防抖
    debounce(getUserAppListData, 300),
    {
      deep: true,
    }
  );

  onMounted(() => {
    // 获取路由参数
    const { id, level } = route.query;
    if (id) {
      filterData.value.qryCatIds = Number(id);
      filterData.value.qryCatTyp = CAT_TYPE[level as string][0]; // 默认能力分类
      selectedKeys.value = [Number(id)];
    }
    getUserAppListData();
  });
</script>

<style scoped lang="less">
  .app-market {
    width: 100%;
    max-width: 1440px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
  }

  .search-bar {
    margin: 58px auto 56px;
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    border-radius: 999px;
    padding: 14px 32px;
    box-sizing: border-box;
    border: 1px solid rgb(0 0 0 / 12%);
    background: #fff;

    .search-input {
      flex: 1 1 0%;
      border: none;
      outline: none;
      padding: 12px 0;
      font-size: 14px;
      color: rgb(0 0 0 / 68%);
      line-height: 28px;
      text-align: left;
      background: transparent;
    }

    .search-btn {
      background: none;
      border: none;
      outline: none;
      cursor: pointer;
      padding: 0 4px;
      display: flex;
      align-items: center;
      color: #444;

      svg {
        display: block;
      }
    }
  }

  .content {
    display: flex;
    width: 100%;

    .content-left {
      width: 200px;
      height: calc(100vh - 250px);
      background-color: transparent;
      position: sticky;
      top: 100px;

      :deep(.arco-menu-light),
      :deep(.arco-menu-light .arco-menu-item),
      :deep(.arco-menu-light .arco-menu-group-title),
      :deep(.arco-menu-light .arco-menu-pop-header),
      :deep(.arco-menu-light .arco-menu-inline-header) {
        background-color: transparent;
        font-weight: 500;
        font-size: 14px;
      }

      :deep(.arco-menu-light .arco-menu-group-title) {
        font-size: 16px;
        color: rgb(0 0 0 / 84%);
      }

      :deep(.arco-menu-light .arco-menu-item) {
        font-size: 14px;
        font-weight: 400;
      }
    }
  }
</style>
