<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
    :rules="field.rules"
  >
    <a-spin :loading="loading" class="check-loading">
      <div class="check-list" v-if="!loading && sourceData.length">
        <div class="check-list-all">
          <a-checkbox
            :indeterminate="allCheck.indeterminate"
            v-model="allCheck.isCheck"
            @change="(flag) => handleAllChange(flag)"
          >
            全选
          </a-checkbox>
        </div>
        <div
          v-for="(item, key) in sourceDataMap"
          :key="key"
          class="checked-item"
        >
          <div class="checked-item-checkbox">
            <a-checkbox
              :indeterminate="item.indeterminate"
              v-model="item.isCheck"
              :value="item?.groupValue"
              @change="(flag) => handleItemChange(item, flag)"
            >
              {{ item?.groupLabel }}
            </a-checkbox>
          </div>
          <div class="checked-item-column">
            <div
              class="checked-column-checkbox"
              v-for="child in item.children"
              :key="child.value"
            >
              <a-checkbox
                v-model="child.isCheck"
                :value="child.value"
                @change="handleColumnChange(item)"
              >
                {{ child.label }}
              </a-checkbox>
            </div>
          </div>
        </div>
      </div>
      <a-empty v-else></a-empty>
    </a-spin>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, reactive, ref, watchEffect } from 'vue';
  import { forEach, isFunction, map, reduce } from 'lodash';
  import { CheckSelectField, Option } from '../../../types/form';

  const formData = inject<any>('formData');

  const value = defineModel<any>({
    default: [],
    set(val) {
      props.field.onChange?.(val, formData);
      return val;
    },
  });
  const props = defineProps<{
    field: CheckSelectField;
    path: string;
  }>();

  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const loading = ref(false);
  const sourceData = ref<Option[]>([]);

  const labelKey: string = props.field.source.labelKey || 'label';
  const valueKey: string = props.field.source.valueKey || 'value';
  const groupLabelKey: string =
    props.field.source.groupLabelKey || 'groupLabel';
  const groupValueKey: string =
    props.field.source.groupValueKey || 'groupValue';
  const childrenKey: string = props.field.source.childrenKey || 'children';
  const sourceDataMap = reactive<any>({});
  const allCheck = reactive({
    isCheck: false,
    indeterminate: false,
    label: '全选',
    allLength: 0,
  });

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      loading.value = true;
      sourceData.value = await props.field.source.data(
        props.field,
        formData?.value as any,
        props.path
      );
    } else {
      sourceData.value = props.field.source.data || [];
    }
    sourceData.value?.forEach((item) => {
      allCheck.allLength += item[childrenKey].length;
      sourceDataMap[item[groupValueKey]] = {
        groupLabel: item[groupLabelKey],
        groupValue: item[groupValueKey],
        isCheck: false,
        indeterminate: false,
        children: item[childrenKey].map((child) => {
          return {
            ...child,
            isCheck: child.isCheck || false,
            label: child[labelKey],
            value: child[valueKey],
          };
        }),
      };
    });
    initData();
    loading.value = false;
  }

  function initData() {
    forEach(sourceDataMap, (item) => {
      forEach(item.children, (child) => {
        if (value.value.includes(child[valueKey]) || child.isCheck) {
          child.isCheck = true;
        }
        handleColumnChange(item);
      });
    });
  }

  function handleAllChange(flag: any) {
    forEach(sourceDataMap, (item) => {
      forEach(item.children, (child) => {
        child.isCheck = flag;
        handleColumnChange(item);
      });
    });
  }

  function handleItemChange(item, flag) {
    item.children.forEach((child) => {
      child.isCheck = flag;
    });
    handleColumnChange(item);
  }

  function handleColumnChange(item) {
    const { children } = item;
    const selectLength = children.filter(
      (childItem) => childItem.isCheck
    ).length;
    const { isCheck, indeterminate } = computedCheckStatus(
      selectLength,
      children.length
    );
    item.isCheck = isCheck;
    item.indeterminate = indeterminate;
    emitValues();
    toggleAll();
  }

  function emitValues() {
    const selectIds: any = [];
    forEach(sourceDataMap, (item) => {
      item.children.forEach((child) => {
        if (child.isCheck) {
          selectIds.push(child[valueKey]);
        }
      });
    });
    value.value = selectIds;
  }

  function computedCheckStatus(selectLength, count) {
    let isCheck;
    let indeterminate;
    if (count === selectLength) {
      isCheck = true;
      indeterminate = false;
    } else if (selectLength === 0) {
      isCheck = false;
      indeterminate = false;
    } else {
      isCheck = false;
      indeterminate = true;
    }
    return {
      isCheck,
      indeterminate,
    };
  }

  function toggleAll() {
    const selectLength = reduce(
      sourceDataMap,
      (prev, curr) => {
        return (
          prev + (curr.children?.filter((child) => child.isCheck).length ?? 0)
        );
      },
      0
    );
    const { isCheck, indeterminate } = computedCheckStatus(
      selectLength,
      allCheck.allLength
    );
    allCheck.isCheck = isCheck;
    allCheck.indeterminate = indeterminate;
  }

  watchEffect(() => {
    getSourceData();
  });
</script>

<style lang="less" scoped>
  .check-loading {
    flex: 1;
    width: 100%;
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .check-list {
    border: 1px solid rgb(var(--gray-3));
    width: 100%;

    .check-list-all {
      padding: 5px 8px;
    }

    .checked-item {
      display: flex;
      flex-direction: column;

      .checked-item-checkbox {
        padding: 5px 8px;
        background-color: #f0f0f0;
      }

      .checked-item-column {
        padding: 5px 8px;
        display: flex;
        flex-wrap: wrap;

        .checked-column-checkbox {
          width: 33.3%;
        }
      }
    }
  }
</style>
