import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css';

import { HOME_URL } from '@/constants';
import appRoutes from './routes';
import { NOT_FOUND_ROUTE, LOGIN_ROUTE } from './routes/base';
import createRouteGuard from './guard'; // NProgress Configuration

NProgress.configure({ showSpinner: false });

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: HOME_URL,
    },
    ...appRoutes,
    LOGIN_ROUTE,
    NOT_FOUND_ROUTE,
  ],
  // scrollBehavior(to, from, savedPosition) {
  //   // 如果有保存的位置（如浏览器后退），则恢复到该位置
  //   if (savedPosition) {
  //     return savedPosition;
  //   }
  //   // 否则滚动到顶部
  //   return { top: 0, behavior: 'instant' }; // 使用instant避免动画导致的闪动
  // },
});

createRouteGuard(router);

export default router;
