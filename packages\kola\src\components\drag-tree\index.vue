<template>
  <a-tree
    class="drag-tree"
    :draggable="draggable"
    block-node
    :data="treeDataRef"
    @drop="handleDrop"
  >
    <template #icon v-if="actions">
      <div class="node-action">
        <component
          :is="action.icon"
          v-for="(action, index) in actions"
          :key="action.id ?? index"
          @click="action.onClick"
          class="action-item"
          :size="18"
        />
      </div>
    </template>
  </a-tree>
</template>

<script setup lang="ts">
  import { TreeNodeData } from '@arco-design/web-vue/es/tree/interface';
  import { ref } from 'vue';

  interface DropArgs {
    e: DragEvent;
    dragNode: TreeNodeData;
    dropNode: TreeNodeData;
    dropPosition: number;
  }

  interface ActionProps {
    id?: string;
    icon: string;
    onClick: () => void;
    title?: string;
  }

  const {
    actions,
    treeData,
    draggable = true,
  } = defineProps<{
    actions: ActionProps[];
    treeData: TreeNodeData[];
    draggable: boolean;
  }>();

  const emit = defineEmits<{
    drop: (data: DropArgs) => void;
  }>();

  const treeDataRef = ref(treeData);

  const handleDrop = (params: DropArgs) => {
    const { dragNode, dropNode, dropPosition } = params;
    const data = treeDataRef.value;
    const loop = (dataArr, key, callback) => {
      dataArr.some((item, index, arr) => {
        if (item.key === key) {
          callback(item, index, arr);
          return true;
        }
        if (item.children) {
          return loop(item.children, key, callback);
        }
        return false;
      });
    };

    loop(data, dragNode.key, (_, index, arr) => {
      arr.splice(index, 1);
    });

    if (dropPosition === 0) {
      loop(data, dropNode.key, (item) => {
        item.children = item.children || [];
        item.children.push(dragNode);
      });
    } else {
      loop(data, dropNode.key, (_, index, arr) => {
        arr.splice(dropPosition < 0 ? index : index + 1, 0, dragNode);
      });
    }
    emit('drop', params);
  };
</script>

<style scoped lang="less">
  .drag-tree {
    .node-action {
      display: none;
      padding-right: 5px;
    }
  }

  .drag-tree :deep(.arco-tree-node-icon) {
    position: absolute;
    right: 0;
  }

  .drag-tree :deep(.arco-tree-node-title):hover {
    .node-action {
      display: block;
    }
  }

  .drag-tree :deep(.arco-tree-node-title-text) {
    max-width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .drag-tree {
    .action-item {
      margin-right: 5px;

      &:hover {
        color: rgb(var(--primary-6));
      }
    }
  }
</style>
