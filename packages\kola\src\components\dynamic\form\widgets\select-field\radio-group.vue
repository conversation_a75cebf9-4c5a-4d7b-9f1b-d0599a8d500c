<template>
  <a-radio-group
    v-model="value"
    :type="format === 'radio' ? 'radio' : 'button'"
  >
    <a-tooltip
      v-for="item in sourceData"
      :key="item[valueKey]"
      :disabled="!item?.tooltip"
    >
      <template #content> {{ item?.tooltip }}</template>

      <a-radio
        :value="item[valueKey]"
        :key="item[valueKey]"
        :disabled="!!item.disabled"
      >
        {{ item[labelKey] }}
        <component :is="item.extra" v-if="item.extra"></component>
      </a-radio>
    </a-tooltip>
  </a-radio-group>
</template>

<script setup lang="ts">
  import { Option } from '../../../types/form';

  defineProps<{
    sourceData: Option[];
    valueKey: string;
    labelKey: string;
    format: string;
  }>();

  const value = defineModel<any>();
</script>
