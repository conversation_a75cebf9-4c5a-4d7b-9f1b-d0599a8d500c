<template>
  <div class="user-section" id="accountSection">
    <a-popover
      content-class="user-info-popover"
      popup-container="#accountSection"
      position="br"
    >
      <a-space>
        <a-space direction="vertical">
          <span class="user-name">{{ userStore?.accountInfo?.name }}</span>
          <span class="user-id">ID：{{ userStore?.accountInfo?.id }}</span>
        </a-space>

        <icon-caret-down class="user-info-icon" />
      </a-space>

      <template #content>
        <div class="logout-btn" @click="handleLogout"> 退出登录 </div>
      </template>
    </a-popover>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store';
  import { clearToken } from '@/utils/auth';

  const userStore = useUserStore();
  const router = useRouter();

  const handleLogout = () => {
    clearToken();
    router.push('/');
  };

  onMounted(() => {
    userStore.accountDetail();
  });
</script>

<style scoped lang="less">
  .user-section {
    margin-right: 24px;
    white-space: nowrap;
    cursor: pointer;
    position: relative;

    .user-info-icon {
      color: #9a9a9a;
    }

    &:hover {
      .user-info-icon {
        color: #333;
      }
    }

    :deep(.arco-popover-content) {
      margin-top: 0;
    }

    .logout-btn {
      cursor: pointer;
    }

    .user-name {
      color: #323335;
      font-size: 14px;
    }

    .user-id {
      font-size: 12px;
      color: #969aa0;
    }
  }
</style>
