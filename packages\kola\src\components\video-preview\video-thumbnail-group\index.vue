<template>
  <div class="video-thumbnail-wrapper">
    <VideoThumbnail
      v-for="(video, index) in videos"
      :key="index"
      :video-src="video.videoSrc"
      :cover-url="video.coverUrl"
      :is-group="true"
      :current-index="index"
      :videos="videos"
    />
  </div>
</template>

<script setup lang="ts">
  import { VideoThumbnailGroupProps } from '../interface';
  import VideoThumbnail from '../video-thumbnail/index.vue';

  const { videos } = defineProps<VideoThumbnailGroupProps>();
</script>

<style scoped lang="less">
  .video-thumbnail-wrapper {
    display: flex;
    width: 100%;
    overflow: auto;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #dcdee2;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #aaa;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
</style>
