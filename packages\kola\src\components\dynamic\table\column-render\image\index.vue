<template>
  <a-image
    :style="style"
    :width="width || 60"
    :height="height || 'auto'"
    v-if="props.record?.[props.column.dataIndex]"
    :src="props.record[props.column.dataIndex]"
  />
  <span v-else>-</span>
</template>

<script lang="ts" setup>
  import { RenderProps } from '../type';
  // props
  const props = defineProps<
    RenderProps & {
      width?: number | string;
      height?: number | string;
      style?: Record<string, string>;
    }
  >();
</script>
