import { CSSProperties, RenderFunction } from 'vue';

export type DDetail = {
  record?: any;
  data: DDetailData;
};

export type DDetailData = DDetailItem[] | ((data: any) => DDetailItem[]);

export type DDetailItem = {
  title: string;
  data: { label: string; value: string | RenderFunction }[];
  size?: 'mini' | 'small' | 'medium' | 'large';
  labelStyle?: CSSProperties;
  valueStyle?: CSSProperties;
  column?: number;
  layout?: 'horizontal' | 'vertical' | 'inline-horizontal' | 'inline-vertical';
  itemTextAlign?: 'left' | 'center' | 'right';
};
