import ObsClient from 'esdk-obs-browserjs/src/obs';

import { v4 as uuIdV4 } from 'uuid';

let tokenCatch: any = null;
const options: any = { credentials: 'include' };

function loadParams() {
  if (tokenCatch) {
    return Promise.resolve(tokenCatch);
  }
  return fetch(`/apis/adplan/obs/conf/v1`, options)
    .then((response) => {
      return response.json();
    })
    .then((res) => {
      const { data } = res;
      const params = {
        access_key_id: data.accessKey,
        secret_access_key: data.secretKey,
        server: data.endPoint,
      };
      tokenCatch = params;
      return params;
    });
}

const defaultBucket = 'cy-pic';
let obsClient: any = null;

function obsUpload({ bucket = defaultBucket, obsKey, ...option }) {
  if (obsKey) {
    tokenCatch = obsKey;
  }
  return loadParams().then((params) => {
    if (obsClient === null) {
      tokenCatch.Bucket = bucket;
      obsClient = new ObsClient(obsKey || tokenCatch);
    }
    return clientPut(bucket, option);
  });
}

function clientPut(bucket, option) {
  const { file, filename, path = '', ProgressCallback, cancel } = option;
  let initCancel = false;

  // @ts-ignore
  return new Promise((resolve, reject) => {
    const fileName = filename || `${uuIdV4()}_originFile_${file.name}`;
    const requestOption = {
      Bucket: bucket, // 桶名
      Key: `${path}${fileName}`, // 文件名    此处用的是uuid
      SourceFile: file, // 流文件
      // PartSize: file.size,
    } as Record<string, any>;

    if (ProgressCallback) {
      requestOption.ProgressCallback = (...prams) => {
        if (cancel && requestOption?.cancelHook && !initCancel) {
          initCancel = true;
          cancel(requestOption.cancelHook);
        }
        if (ProgressCallback) {
          ProgressCallback(...prams);
        }
      };
    }
    obsClient.putObject(requestOption, (err) => {
      if (!err) {
        // url  https://Bucket.server/file.name    server不要https
        const url = `https://${bucket}.obs.cn-north-4.myhuaweicloud.com/${path}${encodeURIComponent(
          fileName
        ).replace(/\*/g, '%2A')}`;
        resolve({
          url,
          file,
          obsFileName: fileName,
        });
      } else {
        console.log('err', err);
        reject(err);
      }
    });
  });
}

export default obsUpload;
