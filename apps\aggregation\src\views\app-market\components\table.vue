<template>
  <div class="content-right">
    <div class="filter-bar">
      <div class="filter-bar-left">
        <span class="filter-item"> 筛选</span>
        <span class="filter-item" @click="handleFilterType()">成交量</span>
        <SortIcon width="16" height="16" @click="handleFilterType()" />
        <span class="filter-item">排列方式</span>
        <icon-apps
          @click="handleFilterAlign('grid')"
          :style="{
            color: align === 'grid' ? '#185EFE' : '#000',
            cursor: 'pointer',
          }"
          size="16"
        />
        <icon-menu
          @click="handleFilterAlign('list')"
          :style="{
            color: align === 'list' ? '#185EFE' : '#000',
            cursor: 'pointer',
          }"
          size="16"
        />
      </div>
      <div class="filter-bar-right">共{{ tableData.length }}条</div>
    </div>
    <div class="table" v-if="align === 'grid'">
      <div
        class="card"
        v-for="item in tableData"
        :key="item.name"
        @click="handleDetail(item)"
      >
        <div class="card-header">
          <img class="avatar" :src="item.logo" alt="头像" />
          <TextOverflowTooltip
            :text="getHighlightedTitle(item.name)"
            :lines="1"
            tooltip-position="tl"
            class-name="title"
          />
        </div>
        <TextOverflowTooltip
          :text="item.desc"
          :lines="2"
          class-name="description"
        />
        <div class="card-info">
          <span>成交量：{{ item.sales }}</span>
          <span>接入方式：{{ item.access_type }}</span>
        </div>
        <div class="divider"></div>
        <div class="card-actions">
          <span class="price">
            <span class="price-text">{{ item.price_val }}</span>
            <span class="price-unit">U点/次</span>
          </span>
          <button class="detail-btn">详 情</button>
        </div>
      </div>
    </div>
    <div class="table flex-col" v-else>
      <div
        class="row-card"
        v-for="item in tableData"
        :key="item.id"
        @click="handleDetail(item)"
      >
        <!-- 左侧内容区 -->
        <div class="card-content">
          <img class="avatar" :src="item.logo" alt="头像" />
          <div class="info">
            <!-- eslint-disable-next-line vue/no-v-html -->
            <div class="title" v-html="getHighlightedTitle(item.name)"></div>
            <TextOverflowTooltip
              :text="item.desc"
              :lines="1"
              tooltip-position="tl"
              class-name="desc"
            />
            <div class="info-row">
              <span>成交量：{{ item.sales }}</span>
              <span class="divider-dot">|</span>
              <span>接入方式：{{ item.access_type }}</span>
            </div>
          </div>
        </div>
        <!-- 右侧操作区 -->
        <div class="card-action-area">
          <div class="v-divider"></div>
          <div class="action-content">
            <div class="price"
              ><span class="num">{{ item.price_val }}</span>
              <span class="unit">U点/次</span></div
            >
            <button class="detail-btn">详情</button>
          </div>
        </div>
      </div>
    </div>
    <a-pagination
      :total="total"
      :current="filterData.page"
      :page-size="filterData.limit"
      :page-size-options="[15, 30, 50]"
      @change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      show-total
      show-jumper
      show-page-size
      style="margin: 32px 0 52px; justify-content: flex-end"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import SortIcon from '@/assets/images/sort.svg?component';
  import { highlightSearchText } from '@repo/kola/src/components/assistant/utils/text';
  import TextOverflowTooltip from '@/components/common/TextOverflowTooltip.vue';

  const router = useRouter();
  const { tableData, total } = defineProps<{
    tableData: any[];
    total: number;
  }>();

  const filterData = defineModel<{
    page: number;
    limit: number;
    sortType: 'desc' | 'asc';
    search: string;
  }>('filterData', {
    default: () => ({
      page: 1,
      limit: 10,
      sortType: 'desc',
    }),
  });

  // 搜索后计算高亮后的标题
  const getHighlightedTitle = (itemName: string) => {
    return highlightSearchText(itemName, filterData.value.search || '');
  };

  function handleFilterType() {
    filterData.value.sortType =
      filterData.value.sortType === 'desc' ? 'asc' : 'desc';
  }

  // 排列方式
  const align = ref<'grid' | 'list'>('grid');
  function handleFilterAlign(type: 'grid' | 'list') {
    align.value = type;
  }

  function handleDetail(item: any) {
    router.push(`/app/detail/${item.id}`);
  }

  function handlePageChange(page: number) {
    filterData.value.page = page;
  }

  function handlePageSizeChange(limit: number) {
    filterData.value.limit = limit;
  }
</script>

<style scoped lang="less">
  .content-right {
    flex: 1;
    height: 100%;
    max-width: 1135px;

    .filter-bar {
      width: 100%;
      height: 40px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      margin-bottom: 32px;
      border-radius: 6px;

      .filter-bar-left {
        display: flex;
        align-items: center;
        gap: 10px;

        .filter-item {
          font-size: 14px;
          line-height: 22px;
          color: rgb(0 0 0 / 84%);
        }

        svg {
          cursor: pointer;
          margin-top: 2px;
          color: rgb(0 0 0 / 84%);
        }

        .active {
          color: #376ff3;
        }
      }
    }

    .table {
      display: flex;
      flex-wrap: wrap;
      gap: 32px;

      .card {
        width: calc((100% - 64px) / 3);
        min-width: 227px;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 20px 0 rgb(34 85 179 / 9%);
        padding: 31px 24px 24px;
        display: flex;
        flex-direction: column;

        .card-header {
          display: flex;
          align-items: center;
          gap: 12px;

          .avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgb(0 0 0 / 3%);
          }

          .title {
            font-size: 18px;
            font-weight: 600;
            color: #222;
          }
        }

        .description {
          font-size: 14px;
          color: #666;
          margin: 16px 0 23px;
          line-height: 24px;
          height: 48px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .card-info {
          display: flex;
          justify-content: space-between;
          color: #888;
          font-size: 13px;
        }

        .divider {
          height: 1px;
          background: #f0f0f0;
          margin: 16px 0;
        }

        .card-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .price {
            font-weight: 500;

            .price-text {
              font-size: 28px;
              color: #ff9100;
              margin-right: 4px;
              line-height: 40px;
            }

            .price-unit {
              line-height: 14px;
              font-size: 10px;
              color: rgb(0 0 0 / 84%);
            }
          }

          .detail-btn {
            width: 93px;
            height: 40px;
            background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
            color: #fff;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
          }

          .detail-btn:hover {
            background: #ff7600;
          }
        }
      }

      .row-card {
        display: flex;
        align-items: stretch;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 12px 0 rgb(34 85 179 / 4%);
        padding: 32px 0 32px 28px;
        min-height: 116px;
        width: 100%;
        box-sizing: border-box;
      }

      .card-content {
        width: 100%;
        flex: 1;
        display: flex;
        align-items: flex-start;
        gap: 22px;
        overflow: hidden;
      }

      .avatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgb(0 0 0 / 3%);
        margin-top: 2px;
      }

      .info {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 14px;
        justify-content: space-between;
      }

      .title {
        color: #2b2f37;
        font-size: 20px;
        font-weight: 700;
      }

      .desc {
        color: #7b8897;
        font-size: 15px;
        line-height: 1.7;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 1;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        max-height: 34px;
      }

      .info-row {
        margin-top: 6px;
        color: #bbc3cf;
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 7px;
      }

      .divider-dot {
        color: #e1e6ef;
        font-size: 16px;
        margin: 0 6px;
      }

      .card-action-area {
        display: flex;
        align-items: center;
        min-width: 146px;
        padding-right: 38px;
        position: relative;
      }

      .v-divider {
        width: 1px;
        height: 115px;
        background: #efefef;
        margin-right: 38px;
        margin-left: 6px;
      }

      .action-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 18px;
        min-width: 88px;
      }

      .price {
        color: #ff9100;
        font-size: 22px;
        margin-bottom: 6px;
        min-width: 100px;
        text-align: left;
        font-weight: 500;
      }

      .num {
        font-size: 40px;
      }

      .unit {
        font-size: 10px;
        font-weight: 500;
        margin-left: 2px;
        color: rgb(0 0 0 / 84%);
        vertical-align: middle;
      }

      .detail-btn {
        width: 93px;
        height: 40px;
        background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
        color: #fff;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition: background 0.2s;
      }

      .detail-btn:hover {
        background: linear-gradient(90deg, #ff7600, #ff9100);
      }
    }
  }

  // 高亮样式
  :deep(.highlight) {
    color: #ff7600;
  }
</style>
