<template>
  <div class="scene-row">
    <div
      class="scene-card"
      v-for="item in caseList"
      :key="item.id"
      @click="handleCaseClick(item.id)"
      :style="cardStyle"
    >
      <img
        class="scene-card-img"
        referrerpolicy="no-referrer"
        :src="item.image || item.cover"
        :alt="item.name"
        loading="lazy"
      />
      <div class="scene-card-content">
        <TextOverflowTooltip :text="item.name" class-name="scene-card-title" />
        <TextOverflowTooltip
          :text="item.desc"
          :lines="2"
          class-name="scene-card-desc"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import TextOverflowTooltip from './TextOverflowTooltip.vue';

  const router = useRouter();

  const { caseList, cardStyle } = defineProps<{
    caseList: {
      id: number;
      image?: string;
      cover?: string;
      name: string;
      desc: string;
    }[];
    cardStyle?: string;
  }>();

  const handleCaseClick = (id: number) => {
    router.push(`/case/detail/${id}`);
  };
</script>

<style scoped lang="less">
  .scene-row {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
  }

  .scene-card {
    width: calc((100% - 64px) / 3);
    height: 298px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 10%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .scene-card-img {
    width: 100%;
    height: calc(100% - 112px);
    border-radius: 16px 16px 0 0;
    object-fit: cover;
  }

  .scene-card-content {
    height: 112px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px 20px 16px;
  }

  .scene-card-title {
    height: 44px;
    font-weight: 500;
    font-size: 16px;
    color: #0b2640;
    line-height: 44px;
  }

  .scene-card-desc {
    width: 100%;
    height: 48px;
    font-size: 14px;
    color: rgb(0 0 0 / 52%);
    line-height: 24px;
  }
</style>
