// eslint-disable-next-line import/prefer-default-export
import type { App, Plugin } from 'vue';

// 全局组件
import DynamicForm from './src/components/dynamic/form/index';
import DynamicPage from './src/components/dynamic/page/index';
import DynamicButton, { DButton } from './src/components/dynamic/button/index';
import DynamicTable from './src/components/dynamic/table/index';
import DynamicDetail from './src/components/dynamic/detail/index';
import DynamicTableSelector from './src/components/dynamic/table-selector/index';
import SvgIcon from './src/components/svg-icon/index';

// ===== 导出组件和类型 ======
export type { DButton };
export { default as DragTree } from './src/components/drag-tree/index.vue';
export { default as TagCreator } from './src/components/tag-creator/index.vue';
export { default as ListCard } from './src/components/list-card/index.vue';
export { default as WeekTimePicker } from './src/components/week-time-picker/index.vue';

const components: Record<string, Plugin> = {
  DynamicPage,
  DynamicForm,
  DynamicButton,
  DynamicTable,
  DynamicDetail,
  DynamicTableSelector,
  SvgIcon,
};

/**
 * Install components into the app
 * @param {App} app - The application to install components into
 */
const install = (app: App) => {
  // Iterate over each component and use it in the app
  Object.keys(components).forEach((key) => {
    app.use(components[key]);
  });
};

const Seal = {
  ...components,
  install,
};

export default Seal;
