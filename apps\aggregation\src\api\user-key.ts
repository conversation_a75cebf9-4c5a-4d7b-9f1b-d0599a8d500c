import { http } from '@/utils/http';

interface UserKeyPriv {
  id?: string;
  name?: string;
}

interface ListReq {
  filter_app_ids?: string[];
  filter_statuses?: number[];
  limit?: number;
  page?: number;
  search?: string;
}

interface SaveReq {
  id?: string;
  name?: string;
  priv_apps?: UserKeyPriv[];
}

interface DeleteReq {
  id: string;
}

interface StatusReq {
  id: string;
  status: number;
}

interface PrivReq {
  id: string;
  priv_apps?: UserKeyPriv[];
}

const userKeyApi = {
  // 获取api key列表
  list(data: ListReq) {
    return http.post('/api/v1/user/key/list', data);
  },
  // 保存api key
  save(data: SaveReq) {
    return http.post('/api/v1/user/key/save', data);
  },
  // 删除api key
  delete(data: DeleteReq) {
    return http.post('/api/v1/user/key/delete', data);
  },
  // 启用/禁用api key
  status(data: StatusReq) {
    return http.post('/api/v1/user/key/status', data);
  },
  // api key 权限
  priv(data: PrivReq) {
    return http.post('/api/v1/user/key/priv', data);
  },

  // 接入
  access(data: { id: string; store_app_id: string }) {
    return http.post('/api/v1/user/key/access', data);
  },
};

export default userKeyApi;
