<template>
  <div class="indicator">
    <div
      class="kola-carousel-indicator-wrapper kola-carousel-indicator-wrapper-bottom"
    >
      <div
        class="kola-carousel-indicator kola-carousel-indicator-slider kola-carousel-indicator-bottom"
        ><span
          :style="sliderStyle"
          :class="[`${prefixCls}-item`, `${prefixCls}-item-active`]"
      /></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';

  const props = defineProps<{
    count: number;
    activeIndex: number;
  }>();

  const prefixCls = 'kola-carousel-indicator';

  const sliderStyle = computed(() => {
    const step = 100 / props.count;
    return { width: `${step}%`, left: `${props.activeIndex * step}%` };
  });
</script>

<style lang="less" scoped>
  .kola-carousel-indicator-wrapper-bottom {
    right: 0;
    bottom: 0;
    left: 0;
    height: 48px;

    /* background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 13%,
      rgba(0, 0, 0, 0.15) 100%
    ); */
  }

  .kola-carousel-indicator-wrapper {
    position: absolute;
    z-index: 2;
  }

  .kola-carousel-indicator-slider {
    width: 120px;
    height: 4px;
    background-color: #ddd;
    border-radius: var(--border-radius-medium);
    cursor: pointer;
  }

  .kola-carousel-indicator-bottom {
    bottom: 30px;
    left: 50%;
    transform: translate(-50%);
  }

  .kola-carousel-indicator {
    position: absolute;
    display: flex;
    margin: 0;
    padding: 0;
  }

  .kola-carousel-indicator-slider .kola-carousel-indicator-item {
    position: absolute;
    top: 0;
    height: 100%;
    transition: left 0.3s;
  }

  .kola-carousel-indicator-item:hover,
  .kola-carousel-indicator-item-active {
    background-color: var(--color-white);
  }

  .kola-carousel-indicator-item {
    display: inline-block;
    background-color: #aaa;
    border-radius: var(--border-radius-medium);
    cursor: pointer;
  }
</style>
