<template>
  <div class="container" ref="selectRef">
    <a-select
      v-model="value"
      :placeholder="field.placeholder || `请选择${field.label}`"
      :allow-search="allowSearchVal"
      :allow-clear="field.select?.allowClear"
      :allow-create="field.select?.allowCreate"
      :limit="field.select?.limit"
      :max-tag-count="field.select?.maxTagCount"
      :style="field.style || field.select?.style"
      :loading="selectCtx.loading"
      :filter-option="field.select?.filterOption"
      :value-key="field.select?.valueKey ?? 'value'"
      :multiple="field.format === 'multipleSelect'"
      @popup-visible-change="popupVisibleChange"
      @clear="handleClear"
      @remove="handleRemove"
      @exceed-limit="handleExceedLimit"
      :format-label="field.select?.formatLabel"
      :show-extra-options="field.select?.showExtraOptions"
      :virtual-list-props="virtualListProps"
      :options="sourceDataRef"
      @search="handleSearch"
      :search-delay="field.select?.searchDelay ?? 300"
      :trigger-props="
        field.select?.updateAtScroll ? { updateAtScroll: true } : {}
      "
      v-model:input-value="inputText"
      @paste="handlePaste"
    >
      <template #option="{ data }">
        <a-tooltip
          :content="data.label"
          position="tl"
          v-if="field.select?.tooltip"
        >
          <span>{{ data.label }}</span>
        </a-tooltip>
        <template v-else>
          <component :is="data.render" v-if="isVNode(data.render)" />
          <span v-else> {{ data.label }}</span>
        </template>
      </template>
      <template #header>
        <div style="padding: 6px 12px" v-if="showAll">
          <a-checkbox
            :model-value="selectCtx.checkedAll"
            :indeterminate="selectCtx.indeterminate"
            @change="selectCtx.handleChangeAll"
          >
            {{ showAllText ?? '全选' }}
          </a-checkbox>
        </div>
      </template>
      <template #search-icon></template>
    </a-select>
    <component :is="field.innerExtra" v-if="field.innerExtra"></component>
    <Tail v-if="field.select?.tail" :tail="field.select?.tail" :path="path" />
  </div>
</template>

<script setup lang="ts">
  import {
    computed,
    inject,
    ref,
    Ref,
    isVNode,
    onMounted,
    nextTick,
    onBeforeUnmount,
  } from 'vue';
  import { debounce, uniq } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import { FormData, Option, SelectField } from '../../../../types/form';
  import { SelectContext, selectInjectionKey } from '../context';
  import Tail from './tail.vue';
  import useContinuousSelection from '../hooks/continuous-selection';

  const props = defineProps<{
    sourceData: Option[];
    valueKey: string;
    labelKey: string;
    format: string;
    showAll: boolean;
    showAllText?: string;
    field: SelectField;
    path?: string;
  }>();
  const selectRef = ref();
  const selectCtx = inject<Partial<SelectContext>>(selectInjectionKey, {});
  const formData = inject<Ref<FormData>>('formData');
  const value = defineModel<any>();
  const inputText = ref<string>('');

  const virtualListProps = props.field.select?.openVirtual
    ? { threshold: 30 }
    : undefined;
  const sourceDataRef = computed(() => {
    try {
      return props.sourceData.map((item) => {
        return {
          ...item,
          label: item[props.labelKey],
          value: item[props.valueKey],
        };
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('e', e);
      // eslint-disable-next-line no-console
      console.log('name', props.field.name);
      return [];
    }
  });

  const handleExceedLimit = () => {
    const limit = props.field.select?.limit;
    if (limit) {
      Message.error(`最多选择${limit}个`);
    }
  };
  const handleClear = () => {
    props.field.select?.onClear?.({
      value: value.value,
      formData,
    });
  };
  const handleRemove = (removed) => {
    props.field.select?.onRemove?.({
      value: value.value,
      formData,
      removed,
    });
  };

  function handleSearch(text) {
    if (props.field.select?.isAsyncSearch) {
      selectCtx.updateSearchText?.(text);
    }
  }
  function handlePaste(event) {
    if (!props.field.select?.allowPaste) return;
    const paste = event.clipboardData.getData('text');
    const words = paste.split('\n').filter((word) => !!word);

    if (words.length > 1) {
      event.preventDefault();
      const newWords = words.filter((word) => {
        return sourceDataRef.value.find((item) => item.value === word)?.value;
      });
      value.value = uniq([...value.value, ...newWords]);
    }
  }

  const maxInputLength = props.field.select?.maxInputLength;

  function inputLen(type?: string) {
    const inputNode = selectRef.value?.querySelector('.arco-select-view-input');
    type === 'set'
      ? inputNode?.setAttribute('maxLength', maxInputLength)
      : inputNode?.removeAttribute('maxLength');
  }
  onMounted(() => {
    if (maxInputLength) {
      inputLen('set');
    }
  });
  onBeforeUnmount(() => {
    if (maxInputLength) {
      inputLen();
    }
  });

  /* 连续选择 */

  const { continuousPopupVisibleChange, allowSearchVal } =
    useContinuousSelection({
      isMultipleSelect: props.field.format === 'multipleSelect',
      allowSearch: props.field.select?.allowSearch,
    });
  function popupVisibleChange(visible) {
    selectCtx.onPopupVisibleChange?.(visible);
    continuousPopupVisibleChange({
      visible,
      inputText,
    });
    if (visible === false) {
      handleSearch('');
    }
  }

  defineExpose({
    getInputText: () => inputText.value,
  });
</script>

<style scoped lang="less">
  .container {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;

    :deep(> .arco-select-view-multiple) {
      > .arco-select-view-inner {
        > .arco-select-view-tag {
          word-break: break-all;
        }
      }
    }
  }
</style>
