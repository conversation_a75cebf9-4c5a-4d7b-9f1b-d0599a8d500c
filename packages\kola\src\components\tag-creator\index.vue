<template>
  <div>
    <a-input
      :placeholder="placeholderText"
      v-model="tagInput"
      @press-enter="addTags"
      class="create-tag"
    >
      <template #append>
        <a-button type="primary" class="create-btn" @click="addTags"
          >添加（回车键）
        </a-button>
      </template>
    </a-input>
    <a-card title="已添加标签" class="card-wrapper">
      <template #extra>
        <a-button type="text" @click="handleClear">
          清空
          <icon-refresh />
        </a-button>
      </template>
      <a-tag
        v-for="tag in displayTags"
        @close="removeTag(tag.id)"
        :key="tag.id"
        closable
        class="tag-item"
      >
        <span>{{ tag.name }}</span>
      </a-tag>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { Message, useFormItem } from '@arco-design/web-vue';
  import { ref, computed, watch } from 'vue';
  import { uniqueId } from 'lodash';

  const tagInput = ref<string>('');
  const { eventHandlers } = useFormItem();
  const tags = defineModel<string[]>({ default: [] });

  watch(tags, () => {
    eventHandlers.value?.onChange?.();
  });

  const props = withDefaults(
    defineProps<{
      params?: { minTagLength?: number; maxTagLength?: number };
    }>(),
    {
      params: () => ({
        minTagLength: 2,
        maxTagLength: 10,
      }),
    }
  );
  const placeholderText = computed(() => {
    return `空格分隔,最多十个,每个标签不超过${props.params.maxTagLength}个字`;
  });

  const displayTags = computed(() => {
    return tags.value.map((item) => ({
      name: item,
      id: uniqueId(),
    }));
  });

  const addTags = () => {
    const displayTagsLength = displayTags.value.length;
    if (displayTagsLength === 10) {
      Message.error(`最多添加${10}个标签`);
      tagInput.value = '';
      return;
    }

    // 输入去重（自身去重+与旧值去重）
    const inputList = Array.from(
      new Set([...tagInput.value.split(' ').filter((tag) => tag !== '')])
    );
    if (inputList.some((item) => tags.value.includes(item))) {
      Message.error({
        content: '重复添加标签，已自动过滤',
        duration: 3 * 1000,
      });
    }

    const tagInputList = inputList.filter((tag) => !tags.value.includes(tag));

    if (tagInputList.length > 10 - displayTagsLength) {
      Message.warning(`最多添加${10 - displayTagsLength}个标签`);
    }

    const newTags: string[] = [];

    const currentTagsLength = tags.value.length;
    const tagsToAdd = tagInputList.slice(0, 10 - currentTagsLength);

    tagsToAdd.forEach((tag) => {
      if (
        props.params.minTagLength &&
        tag.length < props?.params.minTagLength
      ) {
        Message.error({
          content: `字符数至少 ${props.params.minTagLength} 个，已自动过滤`,
          duration: 3 * 1000,
        });
        return;
      }
      if (tag.length > props?.params?.maxTagLength) {
        Message.error({
          content: `超过${props.params.maxTagLength}个字符，已自动过滤`,
          duration: 3 * 1000,
        });
        return;
      }
      newTags.push(tag);
    });

    const result = [...tags.value, ...newTags];
    tags.value = result;
    tagInput.value = '';
  };

  const removeTag = (index) => {
    const indexToRemove = displayTags.value.findIndex(
      (tag) => tag.id === index
    );
    displayTags.value.splice(indexToRemove, 1);
    tags.value = displayTags.value.map((item) => item.name);
  };

  const handleClear = () => {
    tags.value = [];
  };
</script>

<style scoped lang="less">
  .create-tag {
    width: 400px;

    & :deep(.arco-input-append) {
      padding: 0;
      border: none;
    }

    & :deep(.arco-btn-primary) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .tag-item {
    width: 166px;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .card-wrapper {
    width: 400px;
    margin-top: 20px;

    & :deep(.arco-card-body) {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }

  .create-btn {
    color: #197afb !important;
    background-color: #d6eaff !important;
    box-shadow: -1px 0 0 0 #d6eaff;
  }
</style>
