<template>
  <div class="time" v-if="showStamp">{{ timeStampMap[id] }}</div>
  <div
    :class="messageClass"
    @mouseover="showButton = true"
    @mouseleave="showButton = false"
  >
    <div class="message-wrapper" v-if="loading">
      <a-spin />
    </div>
    <div class="message-wrapper" v-else>
      <div
        class="message-content"
        v-if="type === 'my'"
        v-html="myMessage"
      ></div>
      <div
        class="message-content"
        v-else-if="type === 'assistant' && gradually"
        v-html="displayedMarkdown"
      ></div>
      <!-- eslint-disable-next-line vue/no-v-html -->
      <div v-else v-html="sanitizedMarkdown"></div>

      <div class="message-btn" v-if="type === 'my' && showButton">
        <a-tooltip content="编辑" popup-container="#chat">
          <a-button
            @click="handleEdit"
            class="operation-button"
            type="outline"
            size="mini"
          >
            <template #icon>
              <icon-edit size="16" />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip content="复制" popup-container="#chat">
          <a-button
            @click="handleCopy"
            class="operation-button"
            type="outline"
            size="mini"
          >
            <template #icon>
              <icon-copy size="16" />
            </template>
          </a-button>
        </a-tooltip>
      </div>
      <div class="message-btn" v-if="type === 'assistant' && !talking">
        <a-tooltip content="复制" v-if="showButton" popup-container="#chat">
          <a-button
            @click="handleCopy"
            class="operation-button"
            type="outline"
            size="mini"
          >
            <template #icon>
              <icon-copy size="16" />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip content="重新回答" v-if="showButton" popup-container="#chat">
          <a-button
            @click="handleAgain"
            class="operation-button"
            type="outline"
            size="mini"
          >
            <template #icon>
              <icon-refresh size="16" />
            </template>
          </a-button>
        </a-tooltip>
      </div>
      <div class="message-btn-stop" v-if="type === 'assistant' && talking">
        <a-tooltip content="停止回答" popup-container="#chat">
          <a-button
            @click="handleStop"
            class="operation-button"
            type="outline"
            size="mini"
          >
            <template #icon>
              <icon-stop size="16" />
            </template>
          </a-button>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    computed,
    ref,
    watch,
    onMounted,
    inject,
    Ref,
    onBeforeUnmount,
    watchEffect,
  } from 'vue';
  import MarkdownIt from 'markdown-it';
  import hljs from 'highlight.js';
  import DOMPurify from 'dompurify';
  import 'highlight.js/styles/github.css';
  import { scrollToBottom as scrollToBottomUtil } from '../../utils/scroll';
  import { copyText, stripHtmlTags } from '../../utils/text';
  import websocketApi from '../../apis';
  import { generateRequestId } from '../../utils/uuid';
  import { useUpdateTime } from '../../hooks/use-update-time';
  import useAnswerStatus from '../../hooks/use-answer-status';

  const { answerStatus } = useAnswerStatus();
  const md = new MarkdownIt({
    breaks: true, // 允许换行符转换为 <br>
    highlight(str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang }).value;
        } catch (__) {
          return '';
        }
      }

      return '';
    },
  });

  const { getTimeStampMap } = useUpdateTime();
  const timeStampMap = getTimeStampMap();
  const showButton = ref(false);
  const scrollWrapper = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'scrollWrapper'
  );
  const feedbackRef = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'feedbackRef'
  );
  const inputBoxRef = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'inputBoxRef'
  );

  const props = defineProps<{
    id: number;
    message: string;
    type: 'my' | 'assistant';
    gradually?: boolean;
    scrollToBottom?: boolean;
    searchText?: string;
    params?: any;
    question?: string;
    showStamp?: boolean;
  }>();
  const messageClass = computed(() => {
    return props.type === 'my' ? 'message-item-my' : 'message-item-assistant';
  });
  const talking = ref(false);
  const stoping = ref(false);
  const wheel = ref(false);
  const compiledMarkdown = ref('');
  const uuid = ref('');
  const curMessageId = ref('');

  watchEffect(async () => {
    if (typeof props.message === 'string') {
      compiledMarkdown.value = md.render(props.message);
    }
  });
  const sanitizedMarkdown = computed(() => {
    return DOMPurify.sanitize(compiledMarkdown.value);
  });
  const myMessage = computed(() => {
    return props.message.replace(/\n/g, '<br>');
  });
  const displayedMarkdown = ref('');
  const loading = ref(false);
  const typingSpeed = 20;
  let accumulatedContent = '';

  const connectWebSocket = () => {
    const { qtype, question, libraryId } = props.params;
    uuid.value = generateRequestId();
    talking.value = true;
    answerStatus.value = true;
    loading.value = true;
    let timeoutId: number | null = null;
    if (!wheel.value && props.scrollToBottom) {
      scrollToBottomUtil(scrollWrapper);
    }
    const handleTimeout = () => {
      talking.value = false;
      answerStatus.value = false;
      loading.value = false;
      displayedMarkdown.value = '暂时无法处理请求，请稍后再试';
      if (!wheel.value && props.scrollToBottom) {
        scrollToBottomUtil(scrollWrapper);
      }
      websocketApi.off('onMessage', handleMessage);
    };

    const handleMessage = (res: any) => {
      loading.value = false;
      const {
        code,
        requestId,
        data: { answer, messageId, status },
      } = res;
      if (code === 0 && requestId === uuid.value) {
        if (timeoutId !== null) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        if (answer.length > 0 || status === 'finish') {
          curMessageId.value = messageId;
          appendMarkdown(answer, status === 'finish');
        }
      } else if (code === 1) {
        if (timeoutId !== null) {
          clearTimeout(timeoutId);
          timeoutId = null;
          talking.value = false;
          loading.value = false;
          answerStatus.value = false;
          displayedMarkdown.value = '暂时无法处理请求，请稍后再试';
          if (!wheel.value && props.scrollToBottom) {
            scrollToBottomUtil(scrollWrapper);
          }
        }
      }
    };

    websocketApi.send({
      messageType: 'onMessage',
      requestId: uuid.value,
      data: {
        qtype,
        question,
        libraryId,
      },
    });

    websocketApi.on('onMessage', handleMessage);

    timeoutId = setTimeout(handleTimeout, 30000); // 30秒超时
  };

  let index = 0;
  const appendMarkdown = (newContent, finish = false) => {
    accumulatedContent = `${accumulatedContent}${newContent}`;
    const markdown = md.render(accumulatedContent);
    let sanitizedNewMarkdown = DOMPurify.sanitize(markdown);
    sanitizedNewMarkdown = sanitizedNewMarkdown.replace(
      /<a /g,
      '<a target="_blank" '
    );
    const intervalId = setInterval(() => {
      if (stoping.value) {
        clearInterval(intervalId);
        talking.value = false;
        answerStatus.value = false;
        return;
      }

      if (index <= sanitizedNewMarkdown.length) {
        displayedMarkdown.value = sanitizedNewMarkdown.slice(0, index + 5);
        index += 5;

        if (!wheel.value && props.scrollToBottom) {
          scrollToBottomUtil(scrollWrapper);
        }
      } else {
        clearInterval(intervalId);
        if (finish) {
          talking.value = false;
          answerStatus.value = false;
        }
      }
    }, typingSpeed);
  };

  const handleWheel = () => {
    const element = scrollWrapper?.value;
    if (element) {
      const isAtBottom =
        element.scrollHeight - element.scrollTop <= element.clientHeight;
      wheel.value = !isAtBottom;
    }
  };

  onMounted(() => {
    if (props.params) {
      connectWebSocket();
    }
    scrollWrapper?.value?.addEventListener('wheel', handleWheel);
  });
  onBeforeUnmount(() => {
    scrollWrapper?.value?.removeEventListener('wheel', handleWheel);
  });
  watch(
    () => props.message,
    () => {
      displayedMarkdown.value = '';
      accumulatedContent = '';
    }
  );
  const handleEdit = () => {
    // @ts-ignore
    inputBoxRef?.value?.setInputValue(props.message);
    inputBoxRef?.value?.focus();
  };
  const handleCopy = () => {
    if (props.type === 'my') {
      copyText(props.message);
    } else {
      copyText(stripHtmlTags(displayedMarkdown.value));
    }
    // @ts-ignore
    feedbackRef?.value?.show('复制成功', 'success', 2000);
  };
  const handleStop = () => {
    stoping.value = true;
    websocketApi.send({
      messageType: 'onStopMessage',
      requestId: uuid.value,
      data: {
        messageId: curMessageId.value,
      },
    });
  };
  const handleAgain = () => {
    if (answerStatus.value) {
      return;
    }
    // @ts-ignore
    inputBoxRef?.value?.sendValue(props.question);
  };
</script>

<style scoped>
  .message-item-my {
    width: 100%;
    padding: 8px 20px;
    height: auto;
    display: flex;
    justify-content: flex-end;

    .message-wrapper {
      max-width: 474px;
      padding: 11px 13px;
      height: auto;
      background: rgb(0 184 33 / 48%);
      border-radius: 8px 0 8px 8px;
      position: relative;
      text-align: left;

      .message-btn {
        position: absolute;
        bottom: 0;
        left: -58px;
        display: flex;
      }
    }
  }

  .message-item-assistant {
    width: 100%;
    height: auto;
    padding: 8px 20px;
    display: flex;
    justify-content: flex-start;

    .message-wrapper {
      max-width: 474px;
      padding: 11px 13px;
      padding: 16px;
      height: auto;
      background: #fff;
      border-radius: 0 8px 8px;
      border: 1px solid #d8d8d8;
      position: relative;
      text-align: left;

      .message-btn {
        position: absolute;
        bottom: 0;
        right: -62px;
        display: flex;
      }

      .message-btn-stop {
        position: absolute;
        top: 5px;
        right: 5px;
        display: flex;
      }
    }
  }

  .operation-button {
    margin-right: 4px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: #4e5969;
    border-color: rgb(233 233 233);
  }

  .operation-button:hover:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .time {
    text-align: center;
    color: rgb(0 0 0 / 52%);
    margin-top: 8px;
    font-size: 12px;
  }

  .message-content {
    height: auto;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  pre {
    white-space: pre-wrap; /* 允许换行 */
    word-wrap: break-word; /* 允许长单词换行 */
  }
</style>
