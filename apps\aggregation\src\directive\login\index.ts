import { DirectiveBinding } from 'vue';
import { getToken } from '@/utils/auth';
import globalRouter from '../../router';

const loginDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const handler = (e: Event) => {
      const token = getToken();
      if (token) {
        if (typeof binding.value === 'function') {
          binding.value();
        }
      } else {
        e.stopPropagation(); // 阻止事件冒泡
        const redirectUrl = binding.value;
        globalRouter.push({
          path: '/login',
          query: {
            redirectUrl,
          },
        });
      }
    };

    el.addEventListener('click', handler, true);
    (el as any).authClickHandler = handler;
  },

  unmounted(el: HTMLElement) {
    const handler = (el as any).authClickHandler;
    if (handler) {
      el.removeEventListener('click', handler);
      delete (el as any).authClickHandler;
    }
  },
};

export default loginDirective;
