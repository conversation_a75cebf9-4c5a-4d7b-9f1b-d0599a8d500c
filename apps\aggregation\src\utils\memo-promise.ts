const memoPromise = (task: (param: any) => Promise<any>) => {
  const cache = new Map<string, Promise<any>>();

  return (...args: [any]) => {
    const key = JSON.stringify(args ?? '');
    let promise = cache.get(key);

    if (!promise) {
      promise = task(...args);
      promise.then(() => {
        cache.delete(key);
      });
      cache.set(key, promise);
    }

    return promise;
  };
};

export default memoPromise;
