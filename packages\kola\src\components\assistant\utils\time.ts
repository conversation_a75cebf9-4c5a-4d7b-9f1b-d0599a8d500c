export const formatTimeToDiff = (timeStr: string | undefined | number) => {
  if (!timeStr) {
    return '';
  }
  const time = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - time.getTime();

  // 计算时间差
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds < 60) {
    return '刚刚';
  }
  if (minutes < 60) {
    return `${minutes}分钟前`;
  }
  if (hours < 24) {
    return `${hours}小时前`;
  }
  if (days < 3) {
    return `${days}天前`;
  }
  const showYear = time.getFullYear();
  const showMonth = (time.getMonth() + 1).toString().padStart(2, '0');
  const showDay = time.getDate().toString().padStart(2, '0');
  // const showHours = time.getHours().toString().padStart(2, '0');
  // const shoWMinutes = time.getMinutes().toString().padStart(2, '0');
  // const showSeconds = time.getSeconds().toString().padStart(2, '0');
  return `${showYear}/${showMonth}/${showDay} `;
};
export const formatTimeToDate = (timeStr: string | undefined) => {
  if (!timeStr) {
    return '';
  }
  const time = new Date(timeStr);
  const year = time.getFullYear();
  const month = (time.getMonth() + 1).toString().padStart(2, '0');
  const day = time.getDate().toString().padStart(2, '0');
  return `${year}/${month}/${day}`;
};
