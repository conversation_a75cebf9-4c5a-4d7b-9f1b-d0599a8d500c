import { loadEnv, mergeConfig } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configArcoResolverPlugin from './plugin/arcoResolver';
import configImageminPlugin from './plugin/imagemin';

const mode = process.env.VITE_MODE ?? 'production';

console.log('VITE_MODE:', mode);

const env = loadEnv(mode, process.cwd());

export default mergeConfig(
  {
    base: env.VITE_ASSETS_URL,
    mode,
    plugins: [
      configCompressPlugin('gzip', true),
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      configImageminPlugin(),
      visualizer({
        gzipSize: true,
        brotliSize: true,
        emitFile: false,
        filename: 'test.html',
        open: true,
      }),
    ],
    build: {
      minify: 'esbuild',
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              if (id.includes('arco-design')) return 'arco';
              if (id.includes('echarts')) return 'echarts';
              if (
                id.includes('vue') ||
                id.includes('vue-router') ||
                id.includes('pinia') ||
                id.includes('@vueuse/core') ||
                id.includes('vue-i18n')
              ) {
                return 'vue';
              }
              return 'vendor';
            }
            return null;
          },
        },
      },
      chunkSizeWarningLimit: 2000,
    },
  },
  baseConfig
);
