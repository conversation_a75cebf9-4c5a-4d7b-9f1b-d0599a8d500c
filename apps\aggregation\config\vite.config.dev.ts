import { loadEnv, mergeConfig } from 'vite';
import eslint from 'vite-plugin-eslint';
import baseConfig from './vite.config.base';

const env = loadEnv('all', process.cwd());

const proxyMap = {
  test: {
    api: 'https://test-ai.smartroi.cn',
  },
};

const target: keyof typeof proxyMap = 'test';

export default mergeConfig(
  {
    mode: 'development',
    server: {
      open: true,
      host: '0.0.0.0',
      port: 7666,
      proxy: {
        '/api': {
          target: proxyMap[target].api, // 后台服务器地址
          changeOrigin: true, // 是否允许不同源
          secure: true, // 支持https
          cookieDomainRewrite: {
            '*': '',
          },
          // rewrite: (path: any) => {
          //   return path.replace(/^\/apis/, '');
          // },
        },
      },
      fs: {
        strict: true,
      },
    },
    plugins: [
      eslint({
        cache: false,
        include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
        exclude: ['node_modules'],
      }),
    ],
  },
  baseConfig
);
