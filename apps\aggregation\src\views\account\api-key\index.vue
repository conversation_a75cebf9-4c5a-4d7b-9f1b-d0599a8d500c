<template>
  <AccountPage :table="tableConfig" :filter="formSchema" v-model="formData">
    <template #tips>
      <div class="tips-content">
        API Key 是您请求NovaAI服务的重要凭证。API Key
        长期有效，请您不要将密钥信息共享至公开环境，妥善保管并定期轮换密钥，避免因未经授权的使用造成安全风险或资金损失。
      </div>
    </template>
    <template #action>
      <DynamicButton :config="createActionConfig" ref="createApiKeyRef" />
    </template>

    <a-modal
      v-model:visible="accessModalVisible"
      title="接入应用"
      :footer="false"
      width="1200px"
      title-align="start"
      unmount-on-close
    >
      <AccessContent :api-key="apiKey" />
    </a-modal>
  </AccountPage>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { onMounted, ref, useTemplateRef } from 'vue';
  import userKeyApi from '@/api/user-key';
  import AccountPage from '../components/account-page/index.vue';
  import { formSchema, getTableConfig, createApiKeyConfig } from './config';
  import AccessContent from './access/index.vue';

  const createApiKeyRef = useTemplateRef<any>('createApiKeyRef');
  const createActionConfig = createApiKeyConfig('create');
  const accessModalVisible = ref(false);
  const apiKey = ref('');

  const openAccessModal = (data) => {
    accessModalVisible.value = true;
    apiKey.value = data;
  };

  const formData = ref({
    filter_statuses: [],
    search: '',
  });

  const tableConfig = {
    ...getTableConfig(openAccessModal),
    load: {
      action: async (_filter, pageInfo) => {
        const pageParams = {
          page: pageInfo.pageNum,
          limit: pageInfo.pageSize,
        };

        const res = await userKeyApi.list({
          ...pageParams,
          ...formData.value,
        });

        return {
          data: {
            list: res.data?.list || [],
            total: res.data?.total || 0,
          },
        };
      },
    },
  };

  onMounted(() => {
    const route = useRoute();
    if (route.query.action === 'create') {
      createApiKeyRef.value.handleClick?.();
    }
  });
</script>

<style scoped lang="less">
  .tips-content {
    background: #e7eeff;
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    margin-top: 16px;
    padding: 12px 24px;
  }
</style>
