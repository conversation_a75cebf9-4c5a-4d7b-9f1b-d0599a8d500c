<template>
  <div class="greeting" id="greeting">
    <div class="time">{{ timeGreet }}</div>
    <div class="content">我是AI助理，随时为您提供便捷服务。</div>
    <ModuleCard title="热门内容" :question-list="questionList" type="hot" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import lodash from 'lodash';
  import ModuleCard from '../common/module-card.vue';
  import useDefaultQuestions from '../../hooks/use-default-questions';

  const { getDefaultQuestions } = useDefaultQuestions();
  const questionData = getDefaultQuestions();
  const questionList = computed(() => {
    const arr: any[] = [];
    questionData.value.forEach((item: any) => {
      if (item.list && item.list.length > 0) {
        arr.push([...item.list]);
      }
    });
    return lodash.flatten(arr);
  });
  const timeGreet = ref(getGreeting());
  function getGreeting() {
    const now = new Date();
    const currentHour = now.getHours();

    if (currentHour >= 6 && currentHour < 14) {
      return '上午好';
    }
    if (currentHour >= 14 && currentHour < 19) {
      return '下午好';
    }
    return '晚上好';
  }
</script>

<style scoped>
  .greeting {
    width: 100%;
    height: auto;
    padding: 0 23px;

    .time {
      margin-top: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #000;
      line-height: 18px;
    }

    .content {
      margin-top: 17px;
      font-size: 14px;
      font-weight: 400;
      color: #000;
      line-height: 18px;
    }
  }
</style>
