import { forOwn, isNil, isPlainObject } from 'lodash';
import { Message } from '@arco-design/web-vue';

type TargetContext = '_self' | '_parent' | '_blank' | '_top';
export const openWindow = (
  url: string,
  opts?: { target?: TargetContext; [key: string]: any }
) => {
  const { target = '_blank', ...others } = opts || {};
  window.open(
    url,
    target,
    Object.entries(others)
      .reduce((preValue: string[], curValue) => {
        const [key, value] = curValue;
        return [...preValue, `${key}=${value}`];
      }, [])
      .join(',')
  );
};

export const regexUrl = new RegExp(
  '^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
  'i'
);

/**
 * 以 originalData 为基准循环 从右侧 resValue 中取值， resValue为null 或者 undefined 则继续使用 originalData的值
 * @param { object} target
 * @param { any } source
 * @returns { object }
 */

export function hydrateDeep(target: object, source: any) {
  if (!isPlainObject(target)) {
    return target;
  }
  const result = {};

  forOwn(target, (orValue, orKey) => {
    const value = isPlainObject(source) ? source[orKey] : source;
    if (isPlainObject(orValue)) {
      result[orKey] = hydrateDeep(orValue, value);
    } else {
      result[orKey] = isNil(value) ? orValue : value;
    }
  });

  return result;
}

/**
 * 复制文本
 * @param { string} textValue
 * @returns { text }
 */
// eslint-disable-next-line consistent-return
export function copyText(textValue) {
  if (navigator.clipboard && window.isSecureContext) {
    Message.success('复制成功!');
    return navigator.clipboard.writeText(textValue);
  }
  const textArea = document.createElement('textarea');
  textArea.value = textValue;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  Message.success('复制成功');
  textArea.remove();
}

export function updateURLParams(params) {
  const searchParams = new URLSearchParams(window.location.search);
  // 修改参数值
  Object.keys(params).forEach((key) => {
    searchParams.set(key, params[key]);
  });

  // 创建一个新的 URL，替换当前的 URL 但不刷新页面
  window.history.replaceState(
    {},
    '',
    `${window.location.pathname}?${searchParams.toString()}`
  );
}

export const downloadLink = (url: string) => {
  if (!url) {
    return;
  }
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', 'sdk');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export default null;
