export interface VideoProps {
  videoSrc?: string;
  coverUrl: string;
  imgSrc?: string;
}

export interface VideoThumbnailProps {
  videoSrc?: string;
  coverUrl?: string;
  isGroup?: boolean;
  currentIndex?: number;
  videos?: VideoProps[];
}

export interface VideoThumbnailGroupProps {
  videos?: VideoProps[];
}

export type CarouselTriggerEvent = 'click' | 'hover';

export type CarouselArrowType = 'always' | 'hover' | 'never';

export type CarouselIndicatorType = 'line' | 'dot' | 'slider' | 'never';

export type CarouselIndicatorPosition =
  | 'bottom'
  | 'top'
  | 'left'
  | 'right'
  | 'outer';

export type CarouselAutoPlayConfig = {
  interval?: number;
  hoverToPause?: boolean;
};
