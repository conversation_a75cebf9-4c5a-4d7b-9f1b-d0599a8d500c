<template>
  <section class="dev-testimonials">
    <div class="dev-summary">
      <div class="dev-summary__title">{{ title }}</div>
      <div class="dev-summary__quotes">
        <div class="dev-summary__quote-item">“{{ quotes }}”</div>
      </div>
    </div>
    <div class="dev-cards-container">
      <div
        class="dev-cards-row"
        :class="{ paused: isPaused }"
        @mouseenter="isPaused = true"
        @mouseleave="isPaused = false"
      >
        <template v-for="repeat in 2">
          <article
            class="dev-card"
            v-for="item in developerCommentList"
            :key="item.name + '-' + repeat"
          >
            <header class="dev-card__header">
              <div class="dev-card__avatar" aria-hidden="true">
                <img :src="item.logo" :alt="item.name" loading="lazy" />
              </div>
              <section class="dev-card__meta">
                <h3 class="dev-card__name">{{ item.name }}</h3>
                <p class="dev-card__type">{{ item.type }}</p>
              </section>
            </header>
            <blockquote class="dev-card__quote"
              >“{{ item.content }}”</blockquote
            >
          </article>
        </template>
      </div></div
    >
  </section>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const title = '我们的开发者们有话说：';

  defineProps<{
    quotes: string;
    developerCommentList: {
      content: string;
      logo: string;
      name: string;
      type: string;
    }[];
  }>();

  const isPaused = ref(false);
</script>

<style scoped lang="less">
  .dev-testimonials {
    margin: 100px 0;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .dev-summary {
    width: 40%;
    background: #fff;
    padding: 24px;

    &__title {
      font-size: 24px;
      font-weight: 600;
      color: #000;
    }

    &__quotes {
      margin-top: 35px;
      font-weight: 400;
      font-size: 16px;
      color: rgb(0 0 0 / 84%);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }

  .dev-cards-container {
    overflow: hidden;
    flex: 1;
    mask-image: linear-gradient(
      to right,
      transparent,
      black 10% 90%,
      transparent
    );
  }

  .dev-cards-row {
    display: flex;
    gap: 32px;
    width: max-content;
    animation: dev-cards-scroll 12s linear infinite;
  }

  .dev-cards-row.paused {
    animation-play-state: paused;
  }

  @keyframes dev-cards-scroll {
    0% {
      transform: translateX(0);
    }

    100% {
      // 本身的宽度/2 + 间距/2
      transform: translateX(calc(-50% - 16px));
    }
  }

  .dev-card {
    width: 400px;
    min-width: 400px;
    height: 238px;
    padding: 24px;
    border-radius: 18px;
    background: #fff;
    box-shadow: 0 4px 14px rgb(0 0 0 / 8%);
    color: #555;
    margin-bottom: 14px;

    &__header {
      display: flex;
      align-items: center;
      margin-bottom: 18px;
    }

    &__avatar {
      width: 48px;
      height: 48px;
      flex-shrink: 0;
      border-radius: 50%;
      background: #56666f;
      margin-right: 16px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    &__name {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #222;
    }

    &__type {
      margin: 2px 0 0;
      font-size: 14px;
      color: #999;
    }

    &__quote {
      margin: 0;
      padding-left: 18px;
      font-size: 14px;
      line-height: 1.6;
      color: #777;
    }
  }
</style>
