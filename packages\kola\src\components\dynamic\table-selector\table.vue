<template>
  <a-table
    class="table"
    :row-key="valueKey"
    :columns="columns"
    :data="tableData"
    :row-selection="rowSelection"
    :pagination="pagination"
    :scroll="{ y: '100%', x: '100%' }"
    v-model:selected-keys="selectedKeys"
    @page-size-change="handlePageSizeChange"
    @page-change="handlePageChange"
    @sorter-change="handleSorterChange"
    :loading="loading"
    column-resizable
    :bordered="{
      headerCell: true,
    }"
  >
    <template v-for="(value, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps"></slot>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
  import { computed, ref, watchEffect, onMounted } from 'vue';
  import { pick } from 'lodash';
  import { watchDebounced } from '@vueuse/core';
  import { DTable, SortDirectionType } from '../types/table';
  import { FormData } from '../types/form';

  const loading = ref(false);
  const sorter = ref({
    sortBy: '',
    sortDirection: '',
  });

  const props = withDefaults(
    defineProps<{
      columns: any[];
      load?: DTable['load'];
      filter?: FormData;
      valueKey: string;
      labelKey: string;
      extraFields?: string[];
      isMultiple?: boolean;
      selectAllFields?: boolean;
      showFilterOperation?: boolean;
      hideSelection?: boolean;
      extraValue?: Record<string, any>;
      upToDateTableData?: any;
      customPagination?: any;
      autoLoad?: boolean | undefined;
    }>(),
    {}
  );

  const selectedData = defineModel<Record<string, any>>({ default: [] });

  const pagination = ref(
    typeof props.customPagination === 'boolean'
      ? props.customPagination
      : props.customPagination ?? {
          current: 1,
          pageSize: 10,
          total: 20,
          showPageSize: true,
          showJumper: true,
          size: 'mini',
          showTotal: true,
        }
  );

  const rowSelection = computed(() => {
    if (props.hideSelection) {
      return undefined;
    }
    return props.isMultiple
      ? {
          type: 'checkbox',
          showCheckedAll: true,
        }
      : {
          type: 'radio',
        };
  });

  const tableData = ref([]);
  const selectedKeys = computed({
    get() {
      if (!Array.isArray(selectedData.value)) {
        return [];
      }
      return selectedData.value.map((item) => item[props.valueKey]);
    },
    set(val) {
      selectedData.value = val.map((key) => {
        const item = tableData.value.find(
          (data) => data[props.valueKey] === key
        );

        if (!item) {
          return selectedData.value.find(
            (data) => data[props.valueKey] === key
          );
        }
        if (props.selectAllFields) {
          return item;
        }

        return Object.assign(
          pick(item, [
            props.valueKey,
            props.labelKey,
            ...(props.extraFields ?? []),
          ]),
          props.extraValue ? props.extraValue : {}
        );
      });
    },
  });

  const handleSorterChange = (dataIndex: string, direction: string) => {
    sorter.value.sortBy = dataIndex;
    sorter.value.sortDirection = SortDirectionType[direction];
    refreshTable();
  };

  const handlePageReset = (option) => {
    const resetPageSize = option?.resetPageSize ?? true;
    pagination.value.current = 1;
    if (resetPageSize) {
      pagination.value.pageSize = 10;
    }
  };

  const handlePageChange = (current: number) => {
    pagination.value.current = current;
    refreshTable();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.value.current = 1;
    pagination.value.pageSize = pageSize;
    refreshTable();
  };

  const debouncedFilter = ref({ ...(props.filter ?? {}) });

  watchDebounced(
    () => props?.filter,
    (value) => {
      debouncedFilter.value = { ...value };
    },
    { debounce: props.showFilterOperation ? 0 : 300, deep: true }
  );

  const refreshTable = () => {
    loading.value = true;
    const pageConfig = {
      pageSize: pagination.value.pageSize,
      pageNum: pagination.value.current,
    };

    if (!props.load?.action) {
      tableData.value = props.upToDateTableData;
      pagination.value.total = tableData.value?.length;
      loading.value = false;
      return;
    }

    props?.load
      ?.action(
        debouncedFilter.value,
        pageConfig,
        undefined,
        sorter.value as any
      )
      .then((data: any) => {
        tableData.value = data.list;
        if (pagination.value) {
          pagination.value.total = data.total;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  };

  watchEffect(() => {
    if (!props.autoLoad) return;
    if (props.showFilterOperation) {
      return;
    }

    refreshTable();
  });

  onMounted(() => {
    if (props.showFilterOperation && props.autoLoad) {
      refreshTable();
    }
  });

  defineExpose({
    handlePageReset,
    refreshTable,
    getCurrentRowsData: () => tableData.value,
    pagination: pagination.value,
  });
</script>

<style scoped lang="less">
  .table {
    height: 100%;

    :deep(.arco-table-th) {
      border-right: unset;
      font-size: 12px;
    }

    :deep(thead) {
      &:hover {
        .arco-table-th {
          border-right: 1px solid var(--color-neutral-3);
        }
      }
    }

    :deep(.arco-table-td) {
      height: 48px;
      font-size: 12px;
    }
  }
</style>
