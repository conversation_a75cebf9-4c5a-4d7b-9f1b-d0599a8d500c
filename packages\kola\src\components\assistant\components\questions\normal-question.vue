<template>
  <div class="mask" id="question">
    <div class="drag-handle" @mousedown="startDragging">常见问题</div>
    <feedback ref="feedbackRef" />
    <a-tooltip content="返回" popup-container="#question">
      <a-button
        @click="handleBack"
        class="back-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-left size="14" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip content="关闭" popup-container="#question">
      <a-button
        @click="handleClose"
        class="close-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-close size="13" />
        </template>
      </a-button>
    </a-tooltip>
    <div class="scroll-wrapper" ref="scrollWrapper">
      <ModuleCard
        v-for="item in normalQuestionListData"
        :key="item.classify"
        :title="item.classify"
        :question-list="item.list"
        type="normal"
        @choose="handleChoose"
        popup-container="#question"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { inject, Ref } from 'vue';
  import { useDragging } from '../../utils/dragging';
  import Feedback from '../feedback.vue';
  import ModuleCard from '../common/module-card.vue';
  import useDefaultQuestions from '../../hooks/use-default-questions';

  const emit = defineEmits(['close', 'back', 'choose']);
  const chartWrapper = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'chartWrapper'
  );
  const { getDefaultQuestions } = useDefaultQuestions();
  const normalQuestionListData: any = getDefaultQuestions();
  const { startDragging } = useDragging(chartWrapper);

  const handleClose = () => {
    emit('close');
  };

  const handleBack = () => {
    emit('back');
  };
  const handleChoose = (item: any) => {
    emit('choose', item);
  };
</script>

<style scoped>
  .drag-handle {
    cursor: move;
    padding: 18px 23px 18px 30px;
    padding-bottom: 10px;
    user-select: none;
    width: calc(100% - 108px);
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000;
  }

  .close-button {
    position: absolute;
    top: 18px;
    right: 18px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .close-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .back-button {
    position: absolute;
    top: 16px;
    left: 10px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .back-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .scroll-wrapper {
    overflow-y: auto;
    width: 100%;
    height: calc(100% - 100px);
    padding: 0 23px;
  }
</style>
