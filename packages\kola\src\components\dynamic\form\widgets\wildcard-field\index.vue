<template>
  <a-form-item
    ref="formItemRef"
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <div class="wildcard">
      <div class="wildcard-input">
        <a-textarea
          ref="textareaRef"
          v-model="value"
          show-word-limit
          :disabled="field.disabled"
          :readonly="field.readonly"
          :allow-clear="field.allowClear"
          :placeholder="field.placeholder || `请输入${fieldLabel}`"
          :max-length="field.maxLength"
          :style="field?.style"
          :auto-size="field.autoSize"
          @focus="handleFocus"
        >
        </a-textarea>
        <div v-if="field.innerExtra">
          <component :is="field.innerExtra" />
        </div>
      </div>
      <div>
        通配符：
        <a-button
          v-for="item in field.source.data"
          :key="item.value"
          type="text"
          @click="handleInsertWildCard(item?.value, $event)"
          size="mini"
        >
          <template #icon>
            <icon-plus />
          </template>
          {{ item.label }}
        </a-button>
      </div>
    </div>
    <template #extra v-if="field.extra">
      <component :is="field.extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, nextTick, ref, useTemplateRef } from 'vue';
  import { isFunction } from 'lodash';
  import { useFormItem } from '@arco-design/web-vue';
  import { WildcardField } from '../../../types/form';

  const props = defineProps<{
    field: WildcardField;
    path: string;
    extra?: any;
  }>();
  const { eventHandlers, formItemCtx } = useFormItem();

  const formData = inject('formData');
  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const value = defineModel<string>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });
  const textareaRef = ref();

  const formRef = inject('formRef');
  const isFocus = ref(false);
  const formItemRef = useTemplateRef('formItemRef');
  const handleInsertWildCard = (wildcard: string) => {
    const textareaEl = textareaRef.value.textareaRef;
    const text = textareaEl.value;

    // 1. 获取当前光标位置
    let startPos = textareaEl.selectionStart;
    let endPos = textareaEl.selectionEnd;

    // 2. 判断是否聚焦
    if (!isFocus.value) {
      // 未聚焦时，强制插入到末尾
      startPos = text.length;
      endPos = text.length;
    }

    // 3. 执行插入
    const newText =
      text.substring(0, startPos) + wildcard + text.substring(endPos);

    // 4. 处理长度限制
    if (props.field.maxLength) {
      textareaEl.value = newText.substring(0, props.field.maxLength);
      value.value = textareaEl.value;
    } else {
      textareaEl.value = newText;
      value.value = newText;
    }

    // 5. 恢复光标位置（聚焦时才移动光标）
    if (isFocus.value) {
      const newCursorPos = startPos + wildcard.length;
      textareaEl.setSelectionRange(newCursorPos, newCursorPos);
    }
    nextTick(() => {
      isFocus.value = false;
      const { path, field } = props;
      formRef?.value?.validateField?.(`${path ? `${path}.` : ''}${field.name}`);
    });
  };
  function handleFocus() {
    isFocus.value = true;
  }
</script>

<style lang="less" scoped>
  .wildcard {
    flex: 1;

    .wildcard-input {
      display: flex;
      gap: 16px;
      margin-bottom: 4px;
    }
  }
</style>
