import { App } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>att<PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  EffectScatter<PERSON>hart,
} from 'echarts/charts';
import {
  <PERSON>rid<PERSON>omponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  GeoComponent,
} from 'echarts/components';
import Chart from './chart/index.vue';
import Breadcrumb from './breadcrumb/index.vue';

// Manually introduce ECharts modules to reduce packing size

use([
  GeoComponent,
  CanvasRenderer,
  Bar<PERSON>hart,
  Line<PERSON>hart,
  Pie<PERSON>hart,
  RadarChart,
  EffectScatterChart,
  Map<PERSON>hart,
  <PERSON>atter<PERSON>hart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
]);

export default {
  install(Vue: App) {
    Vue.component('Chart', Chart);
    Vue.component('Breadcrumb', Breadcrumb);
  },
};
