<template>
  <span :style="styleObj">{{ name }}</span>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { isFunction } from 'lodash';
  import { RenderProps } from '../type';

  const props = defineProps<
    RenderProps & {
      map:
        | { value: number | string; label: string; style?: any }[]
        | (() => Promise<
            {
              value: number | string;
              label: string;
              style?: any;
            }[]
          >);
    }
  >();

  const staticMap = ref<
    { value: number | string; label: string; style?: any }[]
  >([]);

  if (isFunction(props.map)) {
    props.map().then((data) => {
      staticMap.value = data;
    });
  } else {
    staticMap.value = props.map;
  }

  const dict = computed(() => {
    return new Map(staticMap.value.map((item) => [item.value, item.label]));
  });

  const name = computed(() => {
    return (
      dict.value.get(props.record[props.column.dataIndex as string]) || '-'
    );
  });

  const styleObj = computed(() => {
    return (
      staticMap.value.find(
        (item) => item.value === props.record[props.column.dataIndex as string]
      )?.style ?? {}
    );
  });
</script>
