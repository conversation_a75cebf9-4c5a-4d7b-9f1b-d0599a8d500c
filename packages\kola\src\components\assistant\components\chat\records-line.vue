<template>
  <div class="records-line">
    <a-divider :margin="10"
      >最近对话记录已收起，
      <span class="expand" @click="handleClick">展开</span>
    </a-divider>
  </div>
</template>

<script setup lang="ts">
  const emit = defineEmits(['expand']);
  const handleClick = () => {
    emit('expand');
  };
</script>

<style scoped>
  .records-line {
    width: calc(100% - 46px);
    margin: 0 auto;

    :deep(.arco-divider-text) {
      font-size: 12px;
      font-weight: 400;
      color: rgb(0 0 0 / 52%);
      background-color: transparent;
    }

    .expand {
      cursor: pointer;
      color: rgb(var(--link-6));
    }
  }
</style>
