import { Ref, nextTick } from 'vue';

export const scrollToBottom = (
  scrollWrapper: Ref<HTMLElement | null | undefined> | undefined
) => {
  nextTick(() => {
    scrollWrapper?.value?.scrollTo({
      top: scrollWrapper?.value.scrollHeight,
      behavior: 'smooth',
    });
  });
};
export const scrollToTop = (
  scrollWrapper: Ref<HTMLElement | null | undefined> | undefined
) => {
  nextTick(() => {
    scrollWrapper?.value?.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  });
};
