import { RequestHttp, RequestHttpConfig } from '@repo/sdk';
import { clearToken, getToken } from '../auth';

// eslint-disable-next-line import/prefer-default-export
export const http = new RequestHttp({
  ...RequestHttpConfig,
  baseURL: '/',
  customInterceptors: (res) => {
    if (res.code === 10002) {
      window.location.href = '/home';
      clearToken();
    }
  },
});

http.service.interceptors.request.use((config) => {
  const token = getToken();
  if (token) {
    config.headers = config.headers || {};
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
