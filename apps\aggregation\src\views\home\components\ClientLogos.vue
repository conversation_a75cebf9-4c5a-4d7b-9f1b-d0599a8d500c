<template>
  <section class="client-logos">
    <span class="client-logos__title">我们合作的客户</span>
    <div class="client-logos__container">
      <div
        class="client-logos__row"
        :class="
          rowIdx === 0 ? 'client-logos__row--left' : 'client-logos__row--right'
        "
        v-for="(_, rowIdx) in 2"
        :key="rowIdx"
        :style="
          rowIdx === 0
            ? { transform: 'translateX(-60px)' }
            : { transform: 'translateX(0)' }
        "
      >
        <template v-for="repeat in 2">
          <div
            class="client-logos__item"
            v-for="item in partnerList"
            :key="item + '-' + repeat"
          >
            <img
              referrerpolicy="no-referrer"
              :src="item.logo"
              :alt="item.name"
              style="object-fit: contain"
              loading="lazy"
            />
          </div>
        </template>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  const { partnerList } = defineProps<{
    partnerList: {
      logo: string;
      name: string;
    }[];
  }>();
</script>

<style scoped lang="less">
  .client-logos {
    margin: 80px 0 0;
    text-align: center;

    &__title {
      height: 44px;
      overflow-wrap: break-word;
      color: rgb(11 38 64 / 100%);
      font-size: 32px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      white-space: nowrap;
      line-height: 44px;
      margin: 0 auto;
      display: block;
      text-align: center;
    }

    &__container {
      position: relative;
      // width: 1440px;
      height: 327px;
      overflow: hidden;
      margin: 48px 0 0;
      mask-image: linear-gradient(
        to right,
        transparent,
        black 10% 90%,
        transparent
      );
    }

    &__row {
      width: max-content;
      min-width: 1328px;
      height: 118px;
      margin-top: 24px;
      gap: 32px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      will-change: transform;
    }

    &__row--left {
      animation: logos-move-left 72s linear infinite;
    }

    &__row--right {
      animation: logos-move-right 72s linear infinite;
    }

    @keyframes logos-move-left {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(calc(-50% - 16px));
      }
    }

    @keyframes logos-move-right {
      0% {
        transform: translateX(calc(-50% - 16px));
      }

      100% {
        transform: translateX(0);
      }
    }

    &__item {
      box-shadow: 0 0 30px 0 rgb(0 0 0 / 4%);
      background-color: rgb(255 255 255 / 100%);
      border-radius: 18px;
      height: 118px;
      width: 240px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 151px;
        height: 64px;
      }
    }
  }
</style>
