<template>
  <a-modal
    width="600px"
    v-model:visible="visible"
    :title="props.modalTitle || '使用临时值'"
    @cancel="handleCancel"
    @before-ok="handleOk"
  >
    <a-form ref="formRef" :model="form" :rules="rules">
      <a-form-item field="text" :label="props.fieldLabel || '自定义值'">
        <a-input
          v-model="form.text"
          :max-length="maxLength ?? 100"
          show-word-limit
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';

  const props = defineProps<{
    existList: any[];
    modalTitle?: string;
    fieldLabel?: string;
    maxLength?: number;
  }>();
  const visible = defineModel<boolean>();
  const emit = defineEmits(['submitCustom']);
  const form = reactive({
    text: '',
  });
  const formRef = ref(null);
  const rules = {
    text: [
      {
        required: true,
        validator: (value, cb) => {
          if (!value || !value.trim()) {
            cb('请输入自定义值');
          }
          const fIndex = props.existList.findIndex(
            (item: any) => item.value === value
          );
          if (fIndex !== -1) {
            cb('已存在该值');
          }
        },
      },
    ],
  };
  const resetForm = () => {
    form.text = '';
    formRef.value?.resetFields();
  };
  const handleOk = async () => {
    return new Promise((resolve, reject) => {
      formRef.value.validate((valid) => {
        if (!valid) {
          emit('submitCustom', {
            label: form.text,
            value: form.text,
            isLeaf: true,
          });
          resetForm();
          resolve(true);
        } else {
          reject(new Error('校验不通过'));
        }
      });
    });
  };
  const handleCancel = () => {
    visible.value = false;
    resetForm();
  };
</script>
