<template>
  <a-space direction="vertical" fill class="title-text-library">
    <a-alert v-if="!hideTip">{{ displayTip }}</a-alert>
    <a-card class="fill" :title="displayTitle">
      <template #extra>
        <a-space>
          <ParseExcel
            v-if="!hideImportExcel"
            :parse-excel-request="parseExcelRequest"
            @parse="onParseExcel"
          ></ParseExcel>
          <SelectDynamicText
            v-if="showDynamicText"
            :dynamic-text-lib-request="dynamicTextLibRequest"
            @insert="insertDynamicText"
            :text-lib-columns="textLibColumns"
          ></SelectDynamicText>
          <a-button type="text" @click="clearAll">清空</a-button>
        </a-space>
      </template>
      <a-space class="card-content" direction="vertical" fill :size="0">
        <a-space class="row" v-for="(item, index) in list" :key="item.key" fill>
          <span class="index" :class="{ error: item.inValid }">
            {{ index + 1 }}
          </span>
          <a-input
            ref="inputRefs"
            class="input"
            :placeholder="placeholder"
            v-model.trim="item.value"
            :error="false"
            :input-attrs="{ 'data-key': item.key }"
            @clear="removeTitle(item.key)"
            @input="onInputChange(item)"
            @focus="onFocusChange(item.key)"
            @click="onFocusChange(item.key)"
            @keydown.enter="pushTitle(item.key)"
            @paste="(event) => onPaste(event, item.key)"
          >
            <template #suffix>
              <icon-close class="remove-icon" @click="removeTitle(item.key)" />
            </template>
          </a-input>
        </a-space>
      </a-space>
    </a-card>
    <a-space direction="vertical">
      <a-space
        wrap
        v-for="(item, index) in errors"
        :key="index"
        align="baseline"
      >
        <span class="error">{{ item.text }}</span>
        <a-space v-if="!onlyOneText" wrap align="center">
          <a-button
            type="text"
            v-for="errorItem in item.error"
            :key="errorItem.key"
            @click="viewErrorLine(errorItem.key)"
          >
            第{{ errorItem.index }}行
          </a-button>
        </a-space>
      </a-space>
    </a-space>
  </a-space>
</template>

<script setup lang="ts">
  import { ref, reactive, nextTick, onMounted, computed, watch } from 'vue';
  import { uniqueId, debounce, isNil, isFunction } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import SelectDynamicText from './select-dynamic-text.vue';
  import ParseExcel from './parse-excel.vue';

  const props = defineProps<{
    min: number;
    max: number;
    limit: number;
    hideImportExcel: boolean;
    showDynamicText: boolean;
    maxDynamicText: number;
    hideTip?: boolean;
    alertMessage?: string;
    symbol?: [string, string];
    textLibColumns?: any[];
    editSingle?: boolean;
    displayTitle?: string;
    placeholderText?: string;
    dynamicTextLibRequest: () => Promise<any>;
    parseExcelRequest: () => Promise<any>;
    keyText?: string;
    customTip?: () => string;
    formatValue?: (value: string) => string;
    extendedValidators: {
      key: string;
      validate: (item: any) => boolean;
      errorText: string;
    }[];
  }>();
  const inputRefs = ref(null);
  const list = reactive<any[]>([]);
  const touched = ref(false);
  const selectionLocation = ref({ line: 0, column: 0 });
  const onFocusChange = debounce(onFocus, 200);
  const onInputChange = debounce(onInput, 200);
  const modalValue = defineModel<string[]>({ default: [] });
  const onlyOneText = computed(() => props.limit === 1);
  const placeholder = computed(() => {
    if (props.placeholderText) {
      return props.placeholderText;
    }
    return onlyOneText.value
      ? `请输入${props.keyText}`
      : `请输入${props.keyText}，回车可换行`;
  });
  const separator = computed(() => {
    return props.symbol || ['{', '}'];
  });
  onMounted(() => {
    if (Array.isArray(modalValue.value) && modalValue.value.length > 0) {
      modalValue.value.forEach((val) => pushTitle(null, val));
    } else {
      pushTitle(null);
    }
    touched.value = true;
  });

  const displayTip = computed(() => {
    if (isFunction(props.customTip)) {
      return props.customTip();
    }
    const tip = props.alertMessage
      ? props.alertMessage
      : `${props.keyText}长度范围为${props.min}-${props.max}个字`;

    return props.editSingle ? tip : `${tip},添加多个${props.keyText}请换行`;
  });

  const displayTitle = computed(() => {
    if (props.displayTitle) {
      return props.displayTitle;
    }
    return props.editSingle
      ? `请输入${props.keyText}`
      : `请输入${props.keyText}(回车可换行)`;
  });
  defineExpose({ validateAll });
  //
  // const errors = computed(() => {
  //   if (!touched.value) return [];
  //   const indexList = (cb) => {
  //     const items: Record<string, any>[] = [];
  //     list.forEach((item, index) => {
  //       if (cb(item)) items.push({ index: index + 1, key: item.key });
  //     });
  //     return items;
  //   };
  //
  //   const lengthError = {
  //     text: `${props.keyText}长度范围为${props.min}-${props.max}`,
  //     error:
  //       !isNil(props.min) && !isNil(props.max)
  //         ? indexList((item) => item.lengthError)
  //         : [],
  //   };
  //   const dynamicTextError = {
  //     text: `${props.keyText}动态词包数最大为${props.maxDynamicText}`,
  //     error: indexList((item) => item.dynamicTextError),
  //   };
  //
  //   const repeatError = {
  //     text: `存在重复${props.keyText}`,
  //     error: indexList((item) => item.repeatError),
  //   };
  //
  //   const tagError = {
  //     text: '存在重复动态词库',
  //     error: indexList((item) => item.tagError),
  //   };
  //
  //   return [lengthError, dynamicTextError, repeatError, tagError].filter(
  //     (item) => item.error.length > 0
  //   );
  // });

  const errors = computed(() => {
    if (!touched.value) return [];
    const indexList = (cb) => {
      const items: Record<string, any>[] = [];
      list.forEach((item, index) => {
        if (cb(item)) items.push({ index: index + 1, key: item.key });
      });
      return items;
    };
    const baseErrors = [
      {
        text: `${props.keyText}长度范围为${props.min}-${props.max}`,
        error:
          !isNil(props.min) && !isNil(props.max)
            ? indexList((item) => item.lengthError)
            : [],
      },
      {
        text: `${props.keyText}动态词包数最大为${props.maxDynamicText}`,
        error: indexList((item) => item.dynamicTextError),
      },
      {
        text: `存在重复${props.keyText}`,
        error: indexList((item) => item.repeatError),
      },
      {
        text: '存在重复动态词库',
        error: indexList((item) => item.tagError),
      },
    ];
    // 扩展错误类型
    const extendedErrors = (props.extendedValidators ?? []).map(
      (validator) => ({
        text: validator.errorText,
        error: indexList((item) => item[`extended_${validator.key}`] === false),
      })
    );

    return [...baseErrors, ...extendedErrors].filter(
      (item) => item.error.length > 0
    );
  });
  function onFocus(key) {
    const index = list.findIndex((item) => item.key === key);
    const currentInputRef = findInputRefByKey(key);

    selectionLocation.value = {
      line: index,
      column: currentInputRef?.inputRef.selectionStart,
    };
  }

  function clearErrors() {
    list.forEach((item) => {
      if (item.inValid) {
        removeTitle(item.key);
      }
    });
  }

  function insertDynamicText(word) {
    const { line, column } = selectionLocation.value;
    const inertItem = list[line];
    const strValue = inertItem.value;
    const wordList = strValue.split('');
    wordList.splice(
      column,
      0,
      `${separator.value[0]}${word.name}${separator.value[1]}`
    );
    inertItem.value = wordList.join('');
    updateModelValue();
    validate(inertItem);
  }

  async function pushTitle(key, defaultValue = '') {
    if (typeof props.limit === 'number' && list.length >= props.limit) return;

    const newItem = {
      key: uniqueId('id'),
      value: isFunction(props.formatValue)
        ? props.formatValue(defaultValue)
        : defaultValue,
      inValid: false,
      lengthError: false,
      dynamicTextError: false,
      repeatError: false,
      tagError: false,
    };
    if (key === null) {
      list.push(newItem);
      updateModelValue();
      return;
    }
    const index = list.findIndex((item) => item.key === key);
    list.splice(index + 1, 0, newItem);
    updateModelValue();
    const nextKey = list[index + 1]?.key;

    if (touched.value) {
      validateAll();
    }

    await nextTick();
    const nextInput = findInputRefByKey(nextKey);
    if (nextInput) {
      nextInput.focus();
    }
  }

  function clearAll() {
    list.splice(0, list.length);
    pushTitle(null);
  }

  function onInput(item) {
    validate(item);
    onFocusChange(item.key);
    updateModelValue();
  }

  function onPaste(event, key) {
    const paste = event.clipboardData.getData('text');

    let words = paste.split('\n').filter((word) => !!word);
    if (words.length > 1) {
      event.preventDefault();

      const index = list.findIndex((item) => item.key === key);
      const hasCurrentValue = !!list[index].value;

      const leftover =
        (props.limit ?? Number.MAX_SAFE_INTEGER) -
        list.length +
        (hasCurrentValue ? 0 : 1);

      if (leftover === 0) {
        Message.warning(`最多可添加${props.limit}个${props.keyText}`);
        return;
      }

      if (words.length > leftover) {
        Message.warning(
          `最多可添加${props.limit}个${props.keyText}，超出部分会截断`
        );
      }

      if (!hasCurrentValue) {
        list[index].value = isFunction(props.formatValue)
          ? props.formatValue(words[0])
          : words[0];
        words = words.slice(1, leftover);
      } else {
        words = words.slice(0, leftover);
      }

      words.reverse().forEach((word) => {
        pushTitle(key, word);
      });
    }
  }

  function removeTitle(key, keepOne = true) {
    if (list.length === 1 && keepOne) return;
    const index = list.findIndex((item) => item.key === key);
    list.splice(index, 1);
    validateAll();
    updateModelValue();
  }

  function validateAll() {
    list.forEach((item) => validate(item));
    return list.some((item) => item.inValid);
  }
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
  function generateDynamicTextReg(delimiters) {
    const openingDelimiter = escapeRegExp(delimiters[0]);
    const closingDelimiter = escapeRegExp(delimiters[1]);
    const dynamicTextReg = new RegExp(
      `${openingDelimiter}([^${closingDelimiter}\\]]+)${closingDelimiter}`,
      'g'
    );
    return dynamicTextReg;
  }

  function generateDuplicateContentReg(delimiters) {
    const openingDelimiter = escapeRegExp(delimiters[0]);
    const closingDelimiter = escapeRegExp(delimiters[1]);
    const pattern = `${openingDelimiter}([^${openingDelimiter}${closingDelimiter}]+)${closingDelimiter}(?=.*${openingDelimiter}\\1${closingDelimiter})`;
    const duplicateContentReg = new RegExp(pattern, 'g');
    return duplicateContentReg;
  }

  function validate(item) {
    const { min, max, maxDynamicText } = props;
    const dynamicTextReg = generateDynamicTextReg(separator.value);
    const validText = item.value.replace(
      dynamicTextReg,
      (match, content) => content
    );
    item.lengthError =
      !isNil(min) && !isNil(max)
        ? validText.length > max || validText.length < min
        : false;
    if (props.showDynamicText) {
      const match = item.value.match(dynamicTextReg);
      item.dynamicTextError = match && match.length > maxDynamicText;

      const duplicateContentReg = generateDuplicateContentReg(separator.value);
      item.tagError = duplicateContentReg.test(item.value);
    }

    item.repeatError = list.some(({ value, key }) => {
      return value && value === item.value && key !== item.key;
    });

    // 外部传入自定义经验
    let extendError = false;
    if (Array.isArray(props.extendedValidators)) {
      const extendedKey = 'extended_';
      props.extendedValidators.forEach((validator) => {
        item[`${extendedKey}${validator.key}`] = validator.validate(item);
      });
      extendError = props.extendedValidators.some(
        (v) => item[`${extendedKey}${v.key}`] === false
      );
    }

    item.inValid =
      item.lengthError ||
      item.dynamicTextError ||
      item.repeatError ||
      item.tagError ||
      extendError;
    return item.inValid;
  }

  function onParseExcel(words: string[]) {
    if (list.length === 1 && list[0].value === '') {
      list.splice(0, 1);
    }
    words.forEach((val) => pushTitle(null, val));
  }

  function updateModelValue() {
    modalValue.value = list.map((item) => item.value);
  }

  function viewErrorLine(key) {
    const inputRef = findInputRefByKey(key);
    inputRef?.focus();
  }

  function findInputRefByKey(key) {
    const refs = inputRefs.value || ([] as any[]);
    return refs.find((item) => item.inputAttrs['data-key'] === key);
  }
</script>

<style lang="less" scoped>
  .title-text-library {
    --danger-6: #fff;

    width: 100%;

    .fill {
      width: 100%;
    }

    .row {
      & > :last-child {
        flex: 1;
      }
    }

    button {
      padding: 0;
      height: auto;
    }

    .index {
      display: block;
      border-right: 1px solid var(--color-neutral-3);
      padding-right: 8px;
      width: 26px;
      text-align: right;
      height: 32px;
      line-height: 32px;
    }

    .error {
      color: #db0011;
    }

    .card-content {
      --danger-6: #fff;

      max-height: 300px;
      overflow-y: auto;
    }

    :deep(.input) {
      background-color: transparent;

      .remove-icon {
        display: none;
        cursor: pointer;
      }

      &:hover {
        background-color: var(--color-fill-3);

        .remove-icon {
          display: inline-flex;
        }
      }
    }
  }
</style>
