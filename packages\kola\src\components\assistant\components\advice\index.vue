<template>
  <div class="mask" id="advice">
    <div class="drag-handle" @mousedown="startDragging">诚优意见反馈中心</div>
    <feedback ref="adviceFeedbackRef" />
    <a-tooltip content="返回" popup-container="#advice">
      <a-button
        @click="handleBack"
        class="back-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-left size="14" />
        </template>
      </a-button>
    </a-tooltip>

    <a-button
      @click="handleAdviceHistory"
      class="advice-button"
      type="outline"
      size="mini"
    >
      我的反馈
    </a-button>

    <a-tooltip content="关闭" popup-container="#advice">
      <a-button
        @click="handleClose"
        class="close-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-close size="13" />
        </template>
      </a-button>
    </a-tooltip>
    <div class="scroll-wrapper" ref="scrollWrapper">
      <div class="tips"
        >您反馈的问题将同步至诚优知识库对应平台的意见反馈模块，将有专业人员进行解答。</div
      >
      <a-form ref="formRef" :model="formData" layout="vertical" :rules="rules">
        <a-form-item field="feedbackModule" label="反馈模块" hide-asterisk>
          <template #label>
            <div class="label-text"><div class="label-tag"></div>反馈模块</div>
          </template>
          <Classify v-model="formData"></Classify>
        </a-form-item>
        <a-form-item field="title" label="反馈内容" hide-asterisk>
          <template #label>
            <div class="label-text"><div class="label-tag"></div>反馈内容</div>
          </template>
          <a-input
            class="input-box"
            v-model="formData.title"
            placeholder="请输入标题"
            :max-length="20"
            show-word-limit
          />
        </a-form-item>
        <a-form-item field="content" label="" hide-asterisk>
          <a-textarea
            class="input-box"
            v-model="formData.content"
            placeholder="您的意见对我们非常宝贵，请详细描述或举例"
            :auto-size="{
              minRows: 12,
              maxRows: 12,
            }"
            :max-length="500"
            show-word-limit
          />
        </a-form-item>
        <a-form-item field="picture" label="">
          <a-upload
            ref="adviceUploadRef"
            list-type="picture-card"
            :file-list="fileList"
            accept=".png,.jpg,.jpeg,"
            multiple
            :custom-request="customRequest"
            @before-upload="beforeUpload"
            @exceed-limit="exceedLimit"
            :on-before-remove="beforeRemove"
            image-preview
            :limit="3"
          />
        </a-form-item>
        <div class="upload-tips">
          <div class="tag">Tips</div>
          支持上传3张，图片格式：jpg、png、jpeg；图片大小：≦10MB
        </div>
      </a-form>
      <a-button
        type="primary"
        class="submit-button"
        @click="handleSubmit"
        :loading="loading"
        :disabled="uploading"
        >提交</a-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
  import { inject, Ref, ref, computed } from 'vue';
  import { uniqueId } from 'lodash';
  import obsUpload from '../../utils/obs-upload';
  import { useDragging } from '../../utils/dragging';
  import Feedback from '../feedback.vue';
  import Classify from './classify.vue';
  import websocketApi from '../../apis';
  import { generateRequestId } from '../../utils/uuid';
  import useDictionary from '../../hooks/use-dictionary';

  const adviceFeedbackRef = ref<HTMLElement | null>(null);
  const { getDictionary } = useDictionary();
  const maxImageSize = 10 * 1024 * 1024;
  const emit = defineEmits(['close', 'back']);
  const loading = ref(false);
  const chartWrapper = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'chartWrapper'
  );
  const formRef = ref(null);
  const rules = {
    classify: [
      {
        required: true,
        message: '请选择反馈模块',
      },
    ],
    title: [
      {
        required: true,
        message: '请输入标题',
      },
    ],
    content: [
      {
        required: true,
        message: '请输入内容',
      },
    ],
  };
  const platformId = inject<any>('platformId');
  const formData = ref<any>({ feedbackModule: platformId.value });
  const fileList = ref<any[]>([]);
  const { startDragging } = useDragging(chartWrapper);
  const handleClose = () => {
    emit('close');
  };

  const handleBack = () => {
    emit('back');
  };
  const configData = getDictionary();
  const handleAdviceHistory = () => {
    window.open(configData.value.myThread, '_blank');
  };
  const submitForm = (form: any) => {
    loading.value = true;
    websocketApi.send({
      messageType: 'onFeedbackQuestion',
      requestId: generateRequestId(),
      data: { ...form },
    });
    websocketApi.on('onFeedbackQuestion', (res) => {
      const { code } = res;
      if (code === 0) {
        loading.value = false;
        // @ts-ignore
        adviceFeedbackRef?.value?.show(`提交成功`, 'success', 2000);
        formData.value = { feedbackModule: platformId.value };
        fileList.value = [];
      } else {
        loading.value = false;
        // @ts-ignore
        adviceFeedbackRef?.value?.show(`提交失败`, 'warning', 2000);
      }
    });
  };
  const handleSubmit = () => {
    // @ts-ignore
    formRef.value.validate().then((res) => {
      if (res) {
        return;
      }
      const images = fileList.value.map((item: any) => item.url);
      submitForm({ ...formData.value, images });
    });
  };
  function isImageType(type) {
    return type.startsWith('image');
  }
  async function beforeUpload(file) {
    if (!isImageType(file.type)) {
      return false;
    }

    if (isImageType(file.type) && maxImageSize < file.size) {
      // @ts-ignore
      adviceFeedbackRef?.value?.show(
        `${file.name}超出图片大小限制`,
        'warning',
        2000
      );
      return false;
    }
    return true;
  }
  function exceedLimit() {
    // @ts-ignoret
    adviceFeedbackRef?.value?.show(`超出文件数量限制`, 'warning', 2000);
  }
  const beforeRemove = (fileItem) => {
    const { uid } = fileItem;
    const index = fileList.value.findIndex((item) => item.uid === uid);

    if (index > -1) {
      fileList.value.splice(index, 1);
    }

    return true;
  };
  const customRequest = (option) => {
    const { onError, onSuccess, fileItem } = option;
    const uid = uniqueId();
    fileList.value.push({
      uid,
      name: fileItem.file.name,
      status: 'uploading',
      url: '',
    });
    obsUpload({
      file: fileItem.file,
      path: 'projects/ai-assistant/image/',
    })
      .then(async (res: any) => {
        const { url } = res;
        if (url) {
          const index = fileList.value.findIndex(
            (item: any) => item.uid === uid
          );
          if (index > -1) {
            fileList.value[index].url = url;
            fileList.value[index].status = 'done';
          }
        } else {
          onError();
        }

        onSuccess();
      })
      .catch(() => {
        onError();
      });
  };
  const uploading = computed(() => {
    return fileList.value.some((item: any) => item.status === 'uploading');
  });
</script>

<style scoped>
  .drag-handle {
    cursor: move;
    padding: 18px 23px 18px 30px;
    padding-bottom: 10px;
    user-select: none;
    width: calc(100% - 108px);
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000;
  }

  .close-button {
    position: absolute;
    top: 18px;
    right: 18px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .close-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .back-button {
    position: absolute;
    top: 16px;
    left: 10px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .back-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .advice-button {
    position: absolute;
    top: 18px;
    right: 44px;
    background: none;
    border: none;
    cursor: pointer;
    border-color: rgb(233 233 233);
    font-size: 14px;
  }

  .advice-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .scroll-wrapper {
    overflow-y: auto;
    width: 100%;
    height: calc(100% - 110px);
    padding: 0 23px;
  }

  .search-input {
    width: 573px;
    height: 32px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d8d8d8;
    margin-left: 24px;
  }

  .tips {
    font-weight: 400;
    font-size: 12px;
    color: rgb(0 0 0 / 32%);
    line-height: 18px;
    text-align: left;
    font-style: normal;
    margin-bottom: 13px;
  }

  .upload-tips {
    width: 100%;
    font-size: 12px;
    color: rgb(0 0 0 / 32%);
    line-height: 20px;

    .tag {
      display: inline-block;
      background-color: #007bff;
      color: white;
      padding: 2px 8px;
      border-radius: 3px;
      font-size: 12px;
      line-height: 14px;
    }
  }

  .label-text {
    font-weight: 500;
    font-size: 14px;
    color: rgb(0 0 0 / 84%);
    line-height: 18px;
  }

  .label-tag {
    width: 8px;
    height: 8px;
    margin-right: 4px;
    background: #f77234;
    border-radius: 50%;
    display: inline-block;
  }

  .input-box {
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d8d8d8;
  }

  .submit-button {
    position: absolute;
    width: 88px;
    height: 32px;
    border-radius: 4px;
    right: 23px;
    bottom: 23px;
  }

  :deep(.arco-form-item) {
    margin-bottom: 8px;
  }
</style>
