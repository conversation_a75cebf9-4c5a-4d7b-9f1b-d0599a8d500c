<template>
  <div class="breadcrumb-navbar">
    <a-breadcrumb separator="|">
      <a-breadcrumb-item style="cursor: pointer" @click="handleBack">
        <icon-arrow-left />
        返回
      </a-breadcrumb-item>
      <a-breadcrumb-item> {{ title }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="user-info">
      <UserInfo />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import UserInfo from '../components/user-info/index.vue';

  const route = useRoute();
  const router = useRouter();
  const { title } = route.matched[0].meta;

  const handleBack = () => {
    router.back();
  };
</script>

<style scoped>
  .breadcrumb-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: transparent;
    height: 60px;
    padding: 0 24px;
  }
</style>
