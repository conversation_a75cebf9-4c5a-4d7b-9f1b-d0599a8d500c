import { computed, Ref } from 'vue';

function useContinuousSelection({ isMultipleSelect, allowSearch }) {
  const allowSearchVal = computed(() => {
    if (isMultipleSelect && allowSearch === true) {
      return { retainInputValue: true };
    }
    return allowSearch;
  });
  function continuousPopupVisibleChange({
    inputText,
    visible,
  }: {
    inputText: Ref<string>;
    visible: boolean;
  }) {
    if (visible === false) {
      if (allowSearchVal.value?.retainInputValue) {
        setTimeout(() => {
          inputText.value = '';
        }, 100);
      }
    }
  }
  return {
    continuousPopupVisibleChange,
    allowSearchVal,
  };
}

export default useContinuousSelection;
