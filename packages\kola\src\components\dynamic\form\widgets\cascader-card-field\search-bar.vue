<template>
  <div style="height: 35px; margin-bottom: 8px">
    <a-select
      :value="value"
      style="width: 400px"
      allow-search
      @search="handleSearch"
      :placeholder="placeholder"
      allow-clear
    >
      <template #arrow-icon>
        <icon-search />
      </template>

      <a-option
        v-for="item of options"
        :key="item.key"
        @click="
        (e: Event) => {
          e.stopPropagation();
          cascaderCtx.handlePathChange?.(item);
        }
      "
      >
        <a-tooltip :content="showPathLabel(item)" position="tl">
          <span> {{ showPathLabel(item) }}</span>
        </a-tooltip>
      </a-option>
    </a-select>
  </div>
</template>

<script setup lang="ts">
  import { inject, ref } from 'vue';
  import { CascaderOptionInfo } from './interface';
  import { searchOptions } from './utils';
  import { CascaderContext, cascaderInjectionKey } from './context';

  const props = defineProps<{
    allOptionMap: Map<string, CascaderOptionInfo>;
    placeholder?: string;
    isSearchValue: boolean;
  }>();
  const options = ref<any>([]);
  const value = ref();
  const cascaderCtx = inject<Partial<CascaderContext>>(
    cascaderInjectionKey,
    {}
  );
  function showPathLabel(item) {
    return item.path
      .map((v) => (props.isSearchValue ? `${v.label}(${v.value})` : v.label))
      .join('/');
  }
  const handleSearch = (keyword: string) => {
    const result = searchOptions(
      keyword,
      [...props.allOptionMap.values()],
      props.isSearchValue
    );
    options.value = result;
  };
</script>
