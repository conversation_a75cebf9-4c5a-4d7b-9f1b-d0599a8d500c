<template>
  <div class="page flex-col">
    <div class="block_2 flex-col">
      <TopHeader />
      <section class="content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in" appear>
            <component
              :is="Component ?? route.matched[0]?.components?.content"
              :key="route.path"
            />
          </transition>
        </router-view>
      </section>
      <AppFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
  import TopHeader from '@/components/common/TopHeader.vue';
  import AppFooter from '@/components/common/AppFooter.vue';
</script>

<style src="@/assets/style/common.less" lang="less" />

<style scoped lang="less">
  .page {
    width: 100%;
    min-height: 100vh;

    .block_2 {
      width: 100%;
      flex: 1;
      background: url('@/assets/images/bg.png') no-repeat;
      background-size: cover;
      justify-content: space-between;

      .content {
        flex: 1;
      }
    }
  }
</style>
