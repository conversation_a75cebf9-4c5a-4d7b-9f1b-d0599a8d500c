<template>
  <div
    class="card-item"
    :style="propsField.cardItemStyle ? propsField.cardItemStyle : {}"
  >
    <div class="title"
      >{{ cardInfo.title }}
      {{
        propsField.isShowNumber && level + 1 === getItemsLength
          ? `${selectedLength}/${showOption.length}`
          : ''
      }}
      <a-button
        v-if="cardInfo.canInput && showCustom"
        size="mini"
        type="text"
        style="padding: 0"
        @click="handleCustom"
        >自定义
      </a-button>
      <a-checkbox
        v-if="cardInfo.isCheckAll"
        @change="handleCheckAll"
        v-model="checkAll"
        :indeterminate="indeterminate"
        >全选</a-checkbox
      >
    </div>
    <div
      class="loading-section"
      v-if="cascaderCtx?.loading && level === cascaderCtx.loadingLevel"
    >
      <icon-loading />
    </div>
    <CascaderColumn
      v-else-if="showOption.length"
      :option="showOption"
      :selected-path="selectedPath"
      :is-multiple="!!cardInfo.multiple"
      :level="props.level"
      :is-show-value="propsField.isShowValue"
    ></CascaderColumn>
    <CustomModal
      v-model="visible"
      @submit-custom="getCustom"
      :exist-list="existList"
      :modal-title="props.modalConfig?.modalTitle"
      :field-label="props.modalConfig?.fieldLabel"
      :max-length="props?.modalConfig?.maxLength"
    ></CustomModal>
    <component
      v-if="visible && props.modalConfig?.customComponent"
      :is="props.modalConfig?.customComponent"
      v-model="visible"
      @submit-custom="getCustom"
      :exist-list="existList"
    ></component>
  </div>
</template>

<script setup lang="ts">
  import { computed, inject, nextTick, ref, watch } from 'vue';
  import CascaderColumn from './cascader-column.vue';
  import { CascaderOptionInfo } from './interface';
  import { CascaderContext, cascaderInjectionKey } from './context';
  import type { CascaderCardItem } from '../../../types/form';
  import CustomModal from './custom-modal.vue';

  const props = defineProps<{
    cardInfo: CascaderCardItem;
    option: CascaderOptionInfo[];
    level: number;
    selectedPath: string[];
    modalConfig?: {
      modalTitle?: string;
      fieldLabel?: string;
      maxLength?: number;
    };
    propsField?: any;
    getItemsLength?: any;
  }>();
  const visible = ref(false);
  const existList = ref([]);
  const cascaderCtx = inject<Partial<CascaderContext>>(
    cascaderInjectionKey,
    {}
  );
  const checkAll: any = ref(false);
  const indeterminate = ref(false);
  const selectedLength = ref(0);
  const showOption = computed(() => {
    if (props.level === 0) {
      return props.option;
    }
    return cascaderCtx.getNextPanel?.(props.level) || [];
  });

  watch(
    () => [cascaderCtx.valueMap, showOption.value],
    () => {
      selectedLength.value = showOption.value.filter((item) =>
        cascaderCtx.valueMap?.has(item.key)
      ).length;

      if (selectedLength.value === 0) {
        // console.log(1)
        checkAll.value = false;
        indeterminate.value = false;
      } else if (selectedLength.value === showOption.value.length) {
        checkAll.value = true;
        indeterminate.value = false;
      } else {
        indeterminate.value = true;
      }
    }
  );
  const handleCustom = () => {
    existList.value = cascaderCtx.getNextPanel?.(props.level);
    visible.value = true;
  };
  const getCustom = (value: any) => {
    const curList = cascaderCtx.getNextPanel?.(props.level);
    const optionList =
      curList?.map((item) => {
        return {
          label: item.label,
          value: item.value,
        };
      }) || [];
    optionList.unshift(value);

    const parentKey =
      curList?.[0]?.parent?.key || props.selectedPath[props.level - 1] || '';
    cascaderCtx.addCustomOptions?.(optionList, parentKey);

    if (props.modalConfig?.selectNewOption) {
      nextTick(() => {
        cascaderCtx.onClickOption?.(
          cascaderCtx.optionMap?.get(`${parentKey}-${value.value}`),
          true
        );
      });
    }
  };

  const showCustom = computed(() => {
    return props.selectedPath.length >= props.level;
  });

  function handleCheckAll(value) {
    cascaderCtx.selectAll?.(showOption.value, value);
    // indeterminate.value = !checkAll.value;
  }
</script>

<style scoped lang="less">
  @import './index.less';

  .card-item {
    flex: 1;
    border-right: 1px solid var(--color-neutral-3);
    width: 200px;

    &:last-child {
      border-right: none;
    }
  }

  .loading-section {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
</style>
