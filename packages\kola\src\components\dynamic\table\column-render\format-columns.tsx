import { omit } from 'lodash';
import { TableColumnData } from '@arco-design/web-vue';
import { Column } from '../../types/table';
import resolveRender from './resolve-render';

type RenderColumnsType = Column & {
  render?: any;
};
function formatColumns(
  columns: RenderColumnsType[],
  { refreshTable }: { refreshTable: () => void }
) {
  const results: TableColumnData[] = [
    ...(columns ?? []).map((item) => {
      const width = item.width ?? 80;
      let minWidth = item.minWidth ?? 80;
      if (minWidth > width) {
        minWidth = width;
      }
      return {
        ...omit(item, ['customRender', 'headerToolTip']),
        render: item.slotName
          ? undefined
          : item.render ?? resolveRender(item, { refreshTable }),
        ellipsis: item.ellipsis ?? true,
        tooltip: item.tooltip ?? true,
        minWidth,
        width,
        slots: {
          title: () => (
            <span>
              {item.title}
              {item.headerToolTip && (
                <a-tooltip content={item.headerToolTip}>
                  <icon-info-circle
                    size={12}
                    style={{
                      marginLeft: '4px',
                      cursor: 'pointer',
                      lineHeight: '12px',
                    }}
                  />
                </a-tooltip>
              )}
            </span>
          ),
        },
      };
    }),
  ];

  return results;
}

export default formatColumns;
