<template>
  <div class="markdown-body" v-html="html" />
</template>

<script setup lang="tsx">
  import { ref, onMounted } from 'vue';
  import MarkdownIt from 'markdown-it';

  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
  });

  const html = ref('');

  const props = defineProps<{
    fileUrl: string;
  }>();

  onMounted(async () => {
    if (!props.fileUrl) return;
    try {
      const res = await fetch(props.fileUrl);
      const markdownText = await res.text();
      html.value = md.render(markdownText);
    } catch (err) {
      html.value = '<p>文档加载失败，请稍后重试。</p>';
    }
  });
</script>

<style>
  @import 'github-markdown-css/github-markdown.css';

  .markdown-body {
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgb(0 0 0 / 4%);
    height: 100%;
    overflow: auto;
  }
</style>
