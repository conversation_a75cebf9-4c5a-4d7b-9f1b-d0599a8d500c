import { http } from '@/utils/http';

interface PageReq {
  page?: number;
  limit?: number;
  search?: string;
}

interface StatsReq extends PageReq {
  id: string;
  sdt?: string;
  edt?: string;
}

interface CreateReq {
  id?: string;
  name?: string;
  store_app_id: number;
  type?: string;
}

const userAppApi = {
  // 获取应用列表
  list(data: PageReq) {
    return http.post('/api/v1/user/app/list', data);
  },

  // 获取应用详情
  detail(data: { id: string }) {
    return http.post('/api/v1/user/app/detail', data);
  },

  // 调用统计
  stats(data: StatsReq) {
    return http.post('/api/v1/user/app/stats', data);
  },

  // api 接入
  apiAccess(data: { id: string; key: string }) {
    return http.post('/api/v1/user/app/access', data);
  },

  // 创建/保存 应用
  create(data: CreateReq) {
    return http.post('/api/v1/user/app/save', data);
  },
};

export default userAppApi;
