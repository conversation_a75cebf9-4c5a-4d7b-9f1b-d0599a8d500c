<template>
  <div class="account-page">
    <div class="title"> {{ $route.matched?.[0]?.meta.title }}</div>

    <slot name="tips"></slot>

    <DynamicForm
      ref="appFormRef"
      :form-schema="filter"
      v-model="formData"
      class="filter"
      v-if="filter"
    />
    <slot> </slot>

    <div class="table-wrapper">
      <div class="action">
        <slot name="action"></slot>
      </div>
      <DynamicTable hide-selection :table="table" ref="tableRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useTemplateRef, watch } from 'vue';
  import { debounce } from 'lodash';

  const tableRef = useTemplateRef<any>('tableRef');

  defineProps<{
    filter?: any;
    table: any;
  }>();

  const formData = defineModel();

  const debouncedRefreshTable = debounce(() => {
    tableRef.value.refreshTable?.({
      resetPagination: true,
    });
  }, 500);

  watch(
    formData,
    () => {
      debouncedRefreshTable();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .account-page {
    width: 100%;
    padding: 24px 240px;
    flex: 1;

    :deep(.arco-form-item) {
      margin-bottom: 0;
    }

    :deep(.arco-form-item-label) {
      font-size: 14px;
      color: rgb(0 0 0 / 84%);
    }

    :deep(.arco-btn-primary) {
      background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
      border-radius: 8px;
      border: unset;
    }
  }

  .title {
    font-weight: 500;
    font-size: 24px;
    color: rgb(0 0 0 / 84%);
    line-height: 33px;
  }

  .filter {
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    margin: 24px 0;
  }

  .table-wrapper {
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    height: auto;
  }

  .action {
    margin-bottom: 16px;
  }
</style>
