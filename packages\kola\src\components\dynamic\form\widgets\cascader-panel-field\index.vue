<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="field.hideLabel"
    :disabled="field.disabled"
  >
    <a-spin class="panel-wrap" :loading="loading">
      <div class="panel-left">
        <div class="panel-search">
          <span class="search-label">搜索</span>
          <a-select
            :placeholder="field.searchPlaceholder"
            :options="leafSourceData"
            allow-search
            v-model:input-value="searchText"
            v-model:model-value="searchSelected"
            :virtual-list-props="{ height: 200 }"
            @change="handleSearchChange"
          >
            <template #option="{ data }">
              <span>{{ data.concatLabel }}</span>
            </template>
          </a-select>
        </div>
        <div class="panel-body">
          <div class="body-head">
            <span>{{ field.panelTitle }}</span>
            <a-checkbox
              :disabled="leafSourceData.length <= 0"
              :model-value="checkedAll"
              :indeterminate="indeterminate"
              @change="handleChangeAll"
              >全选</a-checkbox
            >
          </div>
          <a-cascader-panel
            :options="sourceData"
            v-model="value"
            :multiple="true"
            expand-child
            @change="handleChange"
            ref="panelRef"
          />
        </div>
      </div>
      <div class="panel-right">
        <div class="right-head">
          <span
            >已选：{{ selectedData.length }} / {{ leafSourceData.length }}</span
          >
          <a-button
            type="text"
            size="mini"
            :disabled="!selectedData.length"
            @click="handleClear"
            >清空</a-button
          >
        </div>
        <div class="right-body">
          <div class="right-body-search">
            <a-input
              type="text"
              :placeholder="field.searchPlaceholder"
              allow-clear
              @input="rightHandleInput"
              @clear="rightHandleInput('')"
            ></a-input>
          </div>
          <div class="right-body-content g-scroll-bar" ref="rightBodyRef">
            <div
              class="content-item"
              :class="index === selectedData.length - 1"
              v-for="(item, index) in selectedData"
              :key="item.value"
              v-show="!item.isHide"
            >
              <span class="content-text">{{ item.label }}</span>
              <icon-close class="content-close" @click="handleRemove(item)" />
            </div>
            <a-empty v-if="selectedData.length === 0"></a-empty>
          </div>
        </div>
      </div>
    </a-spin>
  </a-form-item>
</template>

<script setup lang="ts">
  import {
    computed,
    inject,
    nextTick,
    ref,
    useTemplateRef,
    watch,
    watchEffect,
  } from 'vue';
  import { debounce, flatMapDeep, flattenDeep, keyBy, reduce } from 'lodash';
  import { CascaderPanelField } from '@/components/dynamic/types/form';
  import useCheckAll from './hooks/check-all';
  import useScrollTarget from '../../../hooks/scroll-target';

  const props = defineProps<{
    field: CascaderPanelField;
    path: string;
  }>();
  const formData = inject('formData');
  // 这里不用小驼峰的原因是因为 提交时候 会把css 中编译成不带小驼峰
  const panelheight = computed(() => {
    return props.field.panelHeight ?? '330px';
  });
  const value = defineModel<any>({
    default: () => [],
    set(val) {
      if (props.field.setter) {
        return props.field.setter(val);
      }
      if (props.field.onChange) {
        setTimeout(() => {
          props.field.onChange?.(val, formData);
        }, 0);
      }
      return val;
    },
  });
  watch(
    () => value.value,
    (newVal) => {
      toggleIndeterminate(newVal, leafSourceData);
    }
  );

  const sourceData = ref<any[]>([]);
  // 源数据
  let flatSourceData: any = [];
  // 摊平的源数据
  const leafSourceData: any = ref([]);
  // 最后一层的数据
  const leafGroupSourceData: any = ref({});
  // 用于展示在右边的最后一层下拉选项对象

  const searchText = ref('');
  const searchSelected = ref('');
  const rightSearchText = ref('');

  function handleSearchChange(val) {
    if (!value.value.includes(val)) {
      value.value.push(val);
      scrollRightLastNode();
    }

    // if (val) {
    //   searchSelected.value = '';
    // }
  }
  const selectedData = computed(() => {
    const values = value.value ?? [];
    let result = values.map((val) => {
      if (leafGroupSourceData.value?.[val]) {
        return {
          ...leafGroupSourceData.value[val],
        };
      }
      return {
        value: val,
        label: val,
      };
    });
    const isHasSearchText = !!rightSearchText.value;
    if (isHasSearchText) {
      const rightSearchVal = rightSearchText.value.toLowerCase();
      result = result.map((item) => {
        return {
          ...item,
          isHide: !`${item.label}`.toLowerCase().includes(rightSearchVal),
        };
      });
    }
    return result;
  });

  const rightHandleInput = debounce((text: string) => {
    rightSearchText.value = text;
  }, 200);
  function handleClear() {
    value.value = [];
  }

  const flattenDeepSourceData = (
    data,
    parentLabel = '',
    parentValue = undefined
  ) => {
    return flatMapDeep(data, (item) => {
      const concatLabel = parentLabel
        ? `${parentLabel} / ${item.label}`
        : item.label;
      const concatParentValue: any = parentValue
        ? [...parentValue, item.value]
        : [item.value];
      const children = item.children
        ? flattenDeepSourceData(item.children, concatLabel, concatParentValue)
        : [];
      const current = {
        ...item,
        concatLabel,
        concatParentValue,
        isLeaf: item.isLeaf ?? children.length <= 0,
      };
      return [current, ...children];
    });
  };

  const style = computed<any>(() => {
    return {
      width: '100%',
      ...(props.field.style ?? {}),
    };
  });

  const loading = ref(false);
  async function getSourceData() {
    let data: any;
    if (typeof props.field.source.data === 'function') {
      try {
        loading.value = true;
        data = await props.field.source.data(
          props.field,
          formData?.value,
          props.path
        );
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('error', error);
        data = [];
      } finally {
        loading.value = false;
      }
    } else {
      data = props.field.source.data || [];
    }
    sourceData.value = data;

    flatSourceData = flattenDeepSourceData(data);
    leafSourceData.value = flatSourceData.filter((item) => item.isLeaf);
    leafGroupSourceData.value = Object.freeze(
      keyBy(leafSourceData.value, 'value')
    );
    if (leafSourceData?.value?.length > 0) {
      toggleIndeterminate(value.value, leafSourceData);
    }
  }

  const rightBodyRef = useTemplateRef<any>('rightBodyRef');
  const { scrollTarget } = useScrollTarget({
    block: 'end',
    behavior: 'instant',
  });
  function handleChange() {
    // scrollRightLastNode();
  }
  async function scrollRightLastNode() {
    await nextTick();
    scrollTarget(rightBodyRef.value, '.content-item:last-child');
  }
  function handleRemove(data) {
    value.value = value.value.filter((item) => item !== data.value);
  }
  const { checkedAll, indeterminate, changeAll, toggleIndeterminate } =
    useCheckAll(props);
  function handleChangeAll(flag) {
    changeAll({
      flag,
      modelValue: value,
      sourceData: leafSourceData,
      labelKey: { value: 'label' },
      valueKey: { value: 'value' },
      inputText: '',
    });
  }
  watchEffect(() => {
    getSourceData();
  });

  function getSelectPath(concatParentValue) {
    const result: string[] = reduce(
      concatParentValue,
      (acc: string[], curr: string | number, index: number) => {
        const prefix: string = acc[index - 1] || '';
        acc.push(index === 0 ? String(curr) : `${prefix}-${curr}`);
        return acc;
      },
      []
    );
    return result;
  }
  const panelRef = useTemplateRef<any>('panelRef');
  // 有值时候设置默认选中
  watch(
    () => leafSourceData.value,
    () => {
      if (value.value?.length > 0) {
        const concatParentValue = selectedData.value?.[0]?.concatParentValue;
        if (concatParentValue?.length > 0) {
          const path = getSelectPath(concatParentValue);
          panelRef.value && (panelRef.value.selectedPath = path);
        }
      }
    },
    {
      once: true,
    }
  );
</script>

<style lang="less" scoped>
  .panel-wrap {
    display: flex;
    flex: 1;
    gap: 16px;
    height: v-bind(panelheight);

    .panel-left {
      flex: 1;
      display: flex;
      flex-direction: column;

      .panel-search {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        height: 32px;

        .search-label {
          flex: 0 0 68px;
          font-size: 13px;
          max-width: 100%;
          color: var(--color-text-2);
          white-space: nowrap;
        }
      }

      .panel-body {
        border-radius: 2px;
        border: 1px solid rgb(0 0 0 / 12%);
        height: calc(100% - 44px);
        flex: 1;

        .body-head {
          display: flex;
          justify-content: space-between;
          align-items: center;
          min-height: 44px;
          background: rgb(0 0 0 / 4%);
          padding: 0 16px;
          border-bottom: 1px solid rgb(0 0 0 / 8%);

          > span {
            font-size: 12px;
            color: rgb(0 0 0 / 84%);
          }
        }

        :deep(> .arco-cascader-panel) {
          display: flex;
          height: calc(100% - 44px);
          overflow: hidden;
          border: none;
          box-shadow: none;

          .arco-cascader-panel-column {
            flex: 1;
            max-height: 100%;

            .arco-scrollbar {
              flex: 1;
            }

            .arco-cascader-column-content {
              flex: 1;
              max-height: calc(v-bind(panelheight) - 45px - 47px);
              height: calc(v-bind(panelheight) - 45px - 47px);
            }
          }
        }
      }
    }

    .panel-right {
      flex: 0 0 200px;
      width: 200px;
      border: 1px solid rgb(0 0 0 / 8%);

      .right-body-search {
        padding: 12px;
      }

      .right-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 44px;
        padding: 0 16px;
        padding-right: 8px;
        border-bottom: 1px solid rgb(0 0 0 / 8%);
        font-size: 12px;
        color: rgb(0 0 0 / 84%);
        background: rgb(0 0 0 / 4%);
      }

      .right-body {
        display: flex;
        flex-direction: column;
        height: calc(v-bind(panelheight) - 44px);
        overflow: hidden;

        .right-body-content {
          padding: 12px;
          padding-top: 0;
          display: flex;
          flex-direction: column;
          gap: 12px;
          height: 100%;
          overflow: auto;
          flex: 1;

          .content-item {
            //min-height: 40px;
            display: flex;
            align-items: center;
            border-color: transparent;
            background: rgb(0 0 0 / 4%);
            border-radius: 2px;
            padding: 9px 12px;
            font-size: 14px;
            justify-content: space-between;

            .content-text {
              word-break: break-all;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }

            .content-close {
              flex: 0 0 16px;
              height: 16px;
              font-size: 12px;
              padding: 2px;
              cursor: pointer;
              border-radius: 50%;
              margin-left: 2px;

              &:hover {
                background: rgb(0 0 0 / 8%);
              }
            }
          }
        }
      }
    }
  }
</style>
