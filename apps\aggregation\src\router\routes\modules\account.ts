import { AppRouteRecordRaw } from '../types';
import { PUBLIC_LAYOUT } from '../base';

const ACCOUNT: AppRouteRecordRaw[] = [
  {
    path: '/account/ak',
    name: 'apiKey',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/account/api-key/index.vue'),
    },
    meta: {
      title: 'APIKey管理',
      requiresAuth: true,
      order: 0,
    },
  },

  {
    path: '/account/recharge',
    name: 'recharge',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/account/recharge/index.vue'),
    },
    meta: {
      title: '充值中心',
      requiresAuth: true,
      order: 0,
    },
  },
  {
    path: '/account/consumption',
    name: 'consumption',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/account/consumption/index.vue'),
    },
    meta: {
      title: '消费统计',
      requiresAuth: true,
      order: 0,
    },
  },
];

export default ACCOUNT;
