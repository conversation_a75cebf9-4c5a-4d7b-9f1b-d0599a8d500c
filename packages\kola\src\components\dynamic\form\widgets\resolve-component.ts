import TextField from './text-field/index.vue';
import Textarea<PERSON>ield from './textarea-field/index.vue';
import NumberField from './number-field/index.vue';
import BooleanField from './boolean-field/index.vue';
import SelectField from './select-field/index.vue';
import ObjectField from './object-field/index.vue';
import ArrayField from './array-field/index.vue';
import CascaderField from './cascader-field/index.vue';
import CustomField from './custom-field/index.vue';
import FileField from './file-field/index.vue';
import DateRangeField from './date-range-field/index.vue';
import SwitchField from './switch-field/index.vue';
import TransferField from './transfer-field/index.vue';
import TreeSelectFieldField from './tree-select-field/index.vue';
import TitleField from './title-field/index.vue';
import TextLibField from './text-lib-field/index.vue';
import CardSelectField from './card-select-field/index.vue';
import <PERSON><PERSON>ange<PERSON>ield from './number-range-field/index.vue';
import WildcardField from './wildcard-field/index.vue';
import PasswordField from './password-field/index.vue';
import DatePickerField from './date-picker-field/index.vue';
import TransferSimpleField from './transfer-simple-field/index.vue';
import CascaderCardField from './cascader-card-field/index.vue';
import CheckSelectField from './check-select-field/index.vue';
import TextTagField from './text-tag-field/index.vue';
import TimeRangeField from './time-range-field/index.vue';
import CascaderPanel from './cascader-panel-field/index.vue';

const fieldMap = {
  text: TextField,
  textarea: TextareaField,
  number: NumberField,
  boolean: BooleanField,
  select: SelectField,
  object: ObjectField,
  array: ArrayField,
  cascader: CascaderField,
  custom: CustomField,
  file: FileField,
  dateRange: DateRangeField,
  datePicker: DatePickerField,
  switch: SwitchField,
  transfer: TransferField,
  treeSelect: TreeSelectFieldField,
  title: TitleField,
  textLib: TextLibField,
  cardSelect: CardSelectField,
  numberRange: NumberRangeField,
  wildcard: WildcardField,
  password: PasswordField,
  transferSimple: TransferSimpleField,
  cascaderCard: CascaderCardField,
  checkSelect: CheckSelectField,
  textTag: TextTagField,
  timeRange: TimeRangeField,
  cascaderPanel: CascaderPanel,
};

const resolveComponent = (type: string) => {
  return fieldMap[type];
};

export default resolveComponent;
