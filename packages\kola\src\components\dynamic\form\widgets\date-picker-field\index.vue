<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
    :hide-asterisk="field.hideAsterisk"
  >
    <a-date-picker
      v-model="value"
      style="width: 100%"
      :show-time="field.showTime"
      :style="field.style"
      :position="field.position"
      :format="timeFormat"
      :disabled-date="field.disabledDate"
      :disabled-time="field.disabledTime"
      :disabled="disabled"
    />
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue';
  import { DatePickerField } from '../../../types/form';

  const props = defineProps<{
    field: DatePickerField;
    path: string;
  }>();

  const timeFormat = computed(() => {
    return props.field.timeFormat ?? 'YYYY-MM-DD';
  });

  const disabled = computed(() => {
    return props.field.disabled?.(value, formData) ?? false;
  });
  const formData = inject('formData');

  const value = defineModel<string | undefined>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      return val;
    },
  });
</script>
