<template>
  <div class="copyable-code-block">
    <!-- 复制按钮 -->
    <a-button class="copy-btn" type="secondary" size="small" @click="copy">
      {{ copied ? '已复制' : '复制' }}

      <template #icon v-if="copied">
        <icon-check />
      </template>
    </a-button>
    <!-- 代码内容 -->
    <pre
      class="code-pre"
    ><code :class="`language-${language}`">{{ code }}</code></pre>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps<{
    code: string;
    language?: string;
  }>();

  const copied = ref(false);

  const copy = async () => {
    await navigator.clipboard.writeText(props.code);
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 1200);
  };
</script>

<style scoped lang="less">
  .copyable-code-block {
    position: relative;
    background: #f6f8fa;
    border-radius: 8px;
    color: rgb(0 0 0 / 84%);

    .copy-btn {
      position: absolute;
      top: 8px;
      right: 24px;
      z-index: 2;
    }

    .code-pre {
      font-size: 16px;
      color: rgb(0 0 0 / 84%);
      font-weight: 400;
      margin: 0;
      padding: 36px 24px 24px;
      overflow: auto;
    }
  }
</style>
