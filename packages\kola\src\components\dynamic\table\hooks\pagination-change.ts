import { Pagination } from '../../types/table';

const usePaginationChange = ({
  refreshTable,
  pagination,
}: {
  refreshTable: () => void;
  pagination: Pagination;
}) => {
  const handlePageChange = (current: number) => {
    pagination.current = current;
    refreshTable();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    refreshTable();
  };

  return {
    handlePageChange,
    handlePageSizeChange,
  };
};

export default usePaginationChange;
