import type { Router } from 'vue-router';
import { setRouteEmitter } from '@/utils/route-listener';
import { getToken } from '@/utils/auth';

function setupPageGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    if (getToken()) {
      next();
    } else if (!to.meta.requiresAuth) {
      next();
    } else {
      next({
        name: 'home',
      });
    }
    // emit route change
    setRouteEmitter(to);
  });

  router.afterEach(async (to, from) => {
    setTimeout(() => {
      window.scrollTo({
        top: 0,
      });
    }, 0);
  });
}

export default function createRouteGuard(router: Router) {
  setupPageGuard(router);
}
