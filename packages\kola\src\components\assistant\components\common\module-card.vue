<template>
  <div style="display: flex; align-items: flex-end">
    <div :class="cardClass">
      <a-list style="width: 100%">
        <template #header>
          <div class="module-card-title"
            >{{ title }}
            <a-button
              class="operation"
              v-if="operation !== ''"
              type="text"
              size="mini"
              @click="handleOperation(operation)"
              >{{ operationMap[operation] }}</a-button
            >
          </div>
        </template>
        <a-list-item v-for="item in showQuestionList" :key="item.libraryId">
          <a-tooltip :content="item.question" :popup-container="popupContainer">
            <div class="module-card-item" @click="handleChoose(item)">{{
              item.question
            }}</div>
          </a-tooltip>
        </a-list-item>
      </a-list>
    </div>
    <a-button
      v-if="type === 'hot'"
      size="mini"
      class="refresh-btn"
      @click="handleClick"
      >换一换</a-button
    >
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, inject, Ref, onMounted, watch } from 'vue';
  import { exchangeQuestion } from '../../utils/exchange-question';

  const inputBoxRef = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'inputBoxRef'
  );
  const handleQuestion = inject<any>('handleQuestion');
  const props = defineProps<{
    title: string;
    questionList: any[];
    type: 'hot' | 'normal';
    popupContainer?: string;
  }>();
  const expanded = ref(false);
  const operationMap = {
    showMore: '查看更多',
    expand: '展开',
    pickUp: '收起',
  };
  const emit = defineEmits(['choose']);
  const handleChoose = (item: any) => {
    if (inputBoxRef?.value) {
      // @ts-ignore
      inputBoxRef.value.chooseValue(item);
    } else {
      emit('choose', item);
    }
  };
  const cardClass = computed(() => {
    if (props.questionList.length < 5) {
      return 'module-card full';
    }
    if (props.type === 'hot') {
      return 'module-card';
    }
    if (expanded.value) {
      return 'module-card full';
    }
    return 'module-card full pickUp';
  });
  const operation = computed(() => {
    if (props.type === 'hot') {
      return 'showMore';
    }
    if (props.questionList.length > 5) {
      if (expanded.value) {
        return 'pickUp';
      }
      return 'expand';
    }
    return '';
  });
  const handleOperation = (type: string) => {
    if (type === 'showMore') {
      handleQuestion();
    }
    if (type === 'expand') {
      expanded.value = true;
    }
    if (type === 'pickUp') {
      expanded.value = false;
    }
  };
  const currentIndex = ref(0);
  const showQuestionList = ref<any[]>([]);
  const handleClick = () => {
    const { list, index } = exchangeQuestion(
      props.questionList,
      currentIndex.value
    );
    currentIndex.value = index;
    showQuestionList.value = list;
  };
  watch(
    () => props.questionList,
    (newVal) => {
      if (newVal) {
        const { list, index } = exchangeQuestion(newVal, 0);
        currentIndex.value = index;
        showQuestionList.value = list;
      }
    }
  );
  onMounted(() => {
    if (props.type === 'hot') {
      const { list, index } = exchangeQuestion(props.questionList, 0);
      currentIndex.value = index;
      showQuestionList.value = list;
    } else {
      showQuestionList.value = props.questionList;
    }
  });
</script>

<style scoped>
  .module-card {
    display: flex;
    width: 420px;
    height: auto;
    background-color: #fff;
    margin-top: 16px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--color-neutral-3);

    &.full {
      width: 100%;

      &.pickUp {
        height: 200px;
      }
    }

    :deep(.arco-list-item) {
      padding: 0 16px 8px !important;
      width: 100%;
      border-bottom: none;
    }

    :deep(.arco-list-item-content) {
      width: 100%;
    }

    :deep(.arco-list-bordered) {
      border: none;
    }

    .module-card-item {
      width: 100%;
      white-space: nowrap; /* 强制文本在一行显示 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis;
      cursor: pointer;
    }

    :deep(.arco-list-item-main) {
      width: 100%;
    }

    :deep(.arco-list-content) {
      padding-top: 8px;
      width: 100%;
    }

    :deep(.arco-list-header) {
      padding: 8px 8px 8px 16px;
    }

    .module-card-title {
      font-weight: 500;
      font-size: 14px;
      color: #000;
      position: relative;

      .operation {
        position: absolute;
        top: 0;
        right: -6px;
        color: rgb(0 0 0 / 52%);
      }
    }
  }

  .refresh-btn {
    /* position: absolute;
      bottom: 0;
      right: -66px; */
    margin-left: 10px;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
    border-radius: 4px;
    border: none;
  }

  .refresh-btn:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }
</style>
