<template>
  <template v-if="!isInline">
    <a-row
      :gutter="props.field.gutter ?? 8"
      v-for="(row, index) in fieldsByRow"
      :key="index"
    >
      <a-col :span="f.span ?? 24" v-for="f in row" :key="f.name" :id="f.name">
        <template
          v-if="
            f.type === 'customView' &&
            (!f.visibleOn || f.visibleOn?.(value, formData))
          "
        >
          <a-form-item
            :tooltip="f.tooltip"
            :label="f.label"
            :hide-label="f.hideLabel"
          >
            <div
              class="custom-view-content"
              :style="(f as customViewField).contentStyle || {}"
            >
              <template
                v-for="ff in (f as customViewField).fields"
                :key="ff.name"
              >
                <WidgetByType
                  :field="ff"
                  v-model="value[ff.name]"
                  v-if="!ff.visibleOn || ff.visibleOn?.(value, formData)"
                  :path="compact([path, field.name]).join('.')"
                />
              </template>
            </div>
          </a-form-item>
        </template>
        <template
          v-else-if="
            f.type === 'viewCard' &&
            (!f.visibleOn || f.visibleOn?.(value, formData))
          "
        >
          <collapse-card
            :title="(f as viewCardField).label"
            :description="(f as viewCardField).description"
            :style="{
              'margin-bottom': '16px',
              'background': '#fff',
              'border': f.hideBorder ? 'none' : '',
            }"
            :content-style="(f as viewCardField).contentStyle || {}"
            :extra="f.extra"
          >
            <template #extra v-if="f.extra">
              <component :is="f.extra"></component>
            </template>
            <template v-for="ff in (f as viewCardField).fields" :key="ff.name">
              <WidgetByType
                :field="ff"
                v-model="value[ff.name]"
                v-if="!ff.visibleOn || ff.visibleOn?.(value, formData)"
                :path="compact([path, field.name]).join('.')"
              />
            </template>
          </collapse-card>
        </template>
        <template v-else>
          <WidgetByType
            :field="f"
            v-model="value[f.name]"
            v-if="!f.visibleOn || f.visibleOn(value, formData)"
            :path="compact([path, field.name]).join('.')"
          />
        </template>
      </a-col>
    </a-row>
  </template>
  <template v-else>
    <template v-for="f in props.field.fields">
      <WidgetByType
        :field="f"
        :key="f.name"
        v-model="value[f.name]"
        v-if="!f.visibleOn || f.visibleOn(value, formData)"
        :path="compact([path, field.name]).join('.')"
      />
    </template>
  </template>
</template>

<script setup lang="ts">
  import { compact } from 'lodash';
  import { ref, computed, inject } from 'vue';
  import WidgetByType from '../../widget-by-type.vue';
  import {
    customViewField,
    Field,
    ObjectField,
    viewCardField,
  } from '../../../../types/form';
  import CollapseCard from '../../collapse-card/index.vue';

  const value = defineModel<Record<string, any>>({ default: {} });

  const formData = ref(inject('formData') ?? {});

  const props = defineProps<{
    field: ObjectField;
    path: string;
  }>();

  const isInline = computed(() => {
    return props.field.layout === 'inline';
  });

  const fieldsByRow = computed(() => {
    const { fields } = props.field;
    const rows: Field[][] = [];

    let currentRow: Field[] = [];
    let currentRowSpan = 0;

    fields.forEach((field) => {
      if (field.visibleOn && !field.visibleOn(value.value, formData.value)) {
        return;
      }

      const span = field.span ?? 24;

      if (field.startNewRow && currentRow.length > 0) {
        rows.push(currentRow);
        currentRow = [field];
        currentRowSpan = span;
        return;
      }

      if (currentRowSpan + span >= 24) {
        currentRow.push(field);
        rows.push(currentRow);
        currentRow = [];
        currentRowSpan = 0;
      } else {
        currentRow.push(field);
        currentRowSpan += span;
      }
    });

    if (currentRow.length > 0) {
      rows.push(currentRow);
    }

    return rows;
  });
</script>

<style scoped lang="less"></style>
