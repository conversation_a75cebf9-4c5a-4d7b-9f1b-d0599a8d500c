# TextOverflowTooltip 组件

一个智能的文本溢出提示组件，当文本超出指定行数时自动显示 tooltip 提示。

## 功能特性

- 🎯 **智能检测**: 自动检测文本是否超出容器，只在需要时显示 tooltip
- 📏 **多行支持**: 支持单行和多行文本溢出检测
- 🔄 **响应式**: 文本内容变化时自动重新检测
- 🎨 **可定制**: 支持自定义样式类名
- ⚡ **高性能**: 使用 nextTick 确保 DOM 更新后再检测

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | string | - | 要显示的文本内容（必填） |
| lines | number | 1 | 限制显示的行数 |
| className | string | '' | 自定义样式类名 |

## 使用示例

### 基础用法

```vue
<template>
  <!-- 单行文本溢出 -->
  <TextOverflowTooltip
    :text="longText"
    :lines="1"
  />

  <!-- 两行文本溢出 -->
  <TextOverflowTooltip
    :text="description"
    :lines="2"
    class-name="custom-style"
  />
</template>

<script setup>
import TextOverflowTooltip from '@/components/common/TextOverflowTooltip.vue';

const longText = '这是一段很长的文本...';
const description = '这是产品描述...';
</script>
```

### 在现有项目中替换

```vue
<!-- 原来的写法 -->
<div class="description two-line-ellipsis">
  <a-tooltip v-if="isShowTooltip" :content="text">
    <span>{{ text }}</span>
  </a-tooltip>
  <span v-else>{{ text }}</span>
</div>

<!-- 使用组件后 -->
<TextOverflowTooltip
  :text="text"
  :lines="2"
  class-name="description"
/>
```

### 动态文本

```vue
<template>
  <TextOverflowTooltip
    :text="dynamicText"
    :lines="3"
  />
  <button @click="changeText">更改文本</button>
</template>

<script setup>
import { ref } from 'vue';

const dynamicText = ref('短文本');

const changeText = () => {
  dynamicText.value = '这是一段更长的文本内容...';
};
</script>
```

## 样式定制

组件内置了基础的文本溢出样式，你也可以通过 `className` 属性添加自定义样式：

```vue
<TextOverflowTooltip
  :text="text"
  :lines="2"
  class-name="my-custom-text"
/>
```

```css
.my-custom-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
```

## 工作原理

1. **单行文本**: 检查 `scrollWidth > clientWidth`
2. **多行文本**: 检查 `scrollHeight > clientHeight`
3. **响应式更新**: 监听 `text` 和 `lines` 属性变化
4. **DOM 同步**: 使用 `nextTick` 确保 DOM 更新完成后检测

## 注意事项

- 确保容器有固定的宽度或最大宽度
- 多行文本需要设置合适的 `line-height`
- 组件会自动处理 DOM 更新时机，无需手动调用检测函数

## 兼容性

- Vue 3.x
- 现代浏览器（支持 CSS `line-clamp`）
- Arco Design Vue（tooltip 组件）
