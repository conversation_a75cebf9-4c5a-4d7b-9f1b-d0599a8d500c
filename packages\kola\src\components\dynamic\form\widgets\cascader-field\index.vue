<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :rules="field.rules"
    :hide-label="field.hideLabel"
    :disabled="field.disabled"
    :style="field.style"
    :label-col-flex="field.labelColFlex"
    ref="cascaderFormItemRef"
  >
    <a-cascader
      :style="style"
      v-model="value"
      :options="sourceData"
      :placeholder="field.placeholder"
      :multiple="field.multiple"
      :allow-clear="true"
      :allow-search="true"
      :format-label="field.formatLabel"
      :path-mode="field.pathMode || false"
      :max-tag-count="field.maxTagCount"
      :trigger-props="triggerProps"
      :field-names="field?.fieldNames"
      :value-key="field?.valueKey"
      :popup-container="field?.popupContainer"
      :check-strictly="field?.checkStrictly"
      @clear="handleClear"
      @on-remove="handleRemove"
      @popup-visible-change="popupVisibleChange"
    />
    <component :is="field.innerExtra" v-if="field.innerExtra"></component>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref, watchEffect } from 'vue';
  import { isEmpty } from 'lodash';
  import { CascaderField } from '../../../types/form';
  import useCascaderRemove from './hooks/cascaderRemove';

  const props = defineProps<{
    field: CascaderField;
    path: string;
  }>();
  const formData = inject('formData');
  const value = defineModel<any>({
    default: '',
    set(val) {
      if (props.field.setter) {
        return props.field.setter(val, formData, props.path);
      }
      if (props.field.onChange) {
        setTimeout(() => {
          props.field.onChange?.(val, formData, props.path);
        }, 0);
      }
      return val;
    },
  });

  const sourceData = ref<any[]>([]);

  const style = computed<any>(() => {
    return {
      width: '100%',
      ...(props.field.style ?? {}),
    };
  });
  const triggerProps = computed(() => {
    const data = {
      ...(props.field.triggerProps ?? {}),
    };
    if (props.field.multiple && props.field.hideLevel1Multiple) {
      if (!data.contentClass) {
        data.contentClass = 'hide-Level1-multiple';
      }
    }
    return isEmpty(data) ? undefined : data;
  });

  async function getSourceData() {
    if (typeof props.field.source.data === 'function') {
      sourceData.value = await props.field.source.data(
        props.field,
        formData?.value,
        props.path
      );
    } else {
      sourceData.value = props.field.source.data || [];
    }
  }

  const handleClear = () => {
    props.field?.onClear?.({
      value: value.value,
      formData,
    });
  };

  function handleRemove() {
    props.field?.onRemove?.({
      value: value.value,
      formData,
    });
  }

  async function popupVisibleChange(visible) {
    props.field?.onPopupVisibleChange?.(visible, formData, value.value);
  }

  const cascaderFormItemRef = ref();

  useCascaderRemove({ handleRemove, props, cascaderFormItemRef });
  watchEffect(() => {
    getSourceData();
  });
</script>

<style lang="less">
  .arco-trigger-content.hide-Level1-multiple {
    > .arco-cascader-panel {
      > .arco-cascader-panel-column:nth-child(1) {
        .arco-cascader-option {
          .arco-checkbox {
            display: none;
          }
        }
      }
    }
  }
</style>
