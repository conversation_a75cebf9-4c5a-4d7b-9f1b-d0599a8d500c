<template>
  <div v-show="welcomeVisible" class="overlay">
    <div class="popup">
      <div class="popup-content">
        <img :src="welcome" class="welcome-img" />
        <div class="close" @click="handleClose"></div>
        <div class="show" @click="handleShow"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  // @ts-ignore
  import welcome from '../../images/welcome.png';

  const props = defineProps({
    welVisible: {
      type: Boolean,
      default: false,
    },
  });
  const welcomeVisible = ref(false);
  const emit = defineEmits(['update:welVisible', 'onShow']);
  watch(
    () => props.welVisible,
    (value) => {
      welcomeVisible.value = value;
    }
  );

  watch(welcomeVisible, (value) => {
    emit('update:welVisible', value);
  });

  const handleClose = (event: Event) => {
    event.stopPropagation();
    welcomeVisible.value = false;
  };

  const handleShow = () => {
    localStorage.setItem('showAiGuide', 'true');
    emit('onShow');
    welcomeVisible.value = false;
  };
</script>

<style scoped>
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
  }

  .popup {
    width: 726px;
    height: 550px;
    position: fixed;
    top: calc(50% - 275px);
    left: calc(50% - 363px);
    z-index: 2000;

    .welcome-img {
      width: 100%;
      height: 100%;
    }
  }

  .popup-content {
    width: 100%;
    height: 100%;
    position: relative;

    .close {
      width: 40px;
      height: 40px;
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
    }

    .show {
      width: 220px;
      height: 55px;
      position: absolute;
      bottom: 0;
      right: calc(50% - 114px);
      cursor: pointer;
    }
  }
</style>
