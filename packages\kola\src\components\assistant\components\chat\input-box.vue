<template>
  <div :class="boxClass">
    <a-textarea
      ref="inputBoxRef"
      v-model="inputValue"
      placeholder="请输入想要了解的广告相关的内容，如什么是DSP平台？"
      :max-length="2000"
      show-word-limit
      @keydown="handleKeyDown"
      :auto-size="{ minRows: 3, maxRows: 3 }"
      style="background-color: #fff; border: none"
    />
    <a-tooltip content="清空" placement="top" popup-container="#chat">
      <a-button
        size="mini"
        type="text"
        class="clear-icon"
        @click="clearMessage"
        v-if="inputValue"
      >
        <template #icon>
          <icon-close size="14" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip
      content="点击发送/Enter发送"
      placement="top"
      popup-container="#chat"
    >
      <a-button
        size="mini"
        type="text"
        class="send-icon"
        @click="sendMessage"
        :disabled="!inputValue || answerStatus"
      >
        <template #icon>
          <icon-send size="18" />
        </template>
      </a-button>
    </a-tooltip>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import useAnswerStatus from '../../hooks/use-answer-status';

  const inputBoxRef = ref(null);
  const { answerStatus } = useAnswerStatus();
  const emit = defineEmits(['submit', 'choose']);
  const inputValue = ref('');
  const handleKeyDown = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (answerStatus.value) {
        return;
      }
      sendMessage();
    }
  };

  const sendMessage = () => {
    if (inputValue.value.trim() === '') {
      inputValue.value = '';
      return;
    }
    emit('submit', inputValue.value);
    inputValue.value = '';
  };
  const sendValue = (value: string) => {
    emit('submit', value);
  };
  const chooseValue = (item: any) => {
    emit('choose', item);
  };
  const clearMessage = () => {
    inputValue.value = '';
  };
  const setInputValue = (value: string) => {
    inputValue.value = value;
  };
  const focus = () => {
    // @ts-ignore
    inputBoxRef.value?.focus();
  };
  const boxClass = computed(() => {
    return inputValue.value ? 'input-box-clear' : 'input-box';
  });
  defineExpose({
    setInputValue,
    sendValue,
    chooseValue,
    focus,
  });
</script>

<style scoped>
  .input-box {
    position: relative;
    border: 1px solid rgb(var(--gray-1));
    border-radius: 4px;

    /* padding: 10px; */
    display: flex;
    align-items: center;
    background-color: #fff;

    :deep(.arco-textarea-word-limit) {
      right: 30px;
      transition: right 0.3s;
    }

    :deep(.arco-btn-icon) {
      z-index: 1002;
    }

    :deep(.arco-textarea) {
      padding: 10px 16px;
      margin-bottom: 24px;
      background-color: #fff;
      font-size: 12px;
      font-weight: 400;
    }
  }

  .input-box-clear {
    position: relative;
    border: 1px solid rgb(var(--gray-1));
    border-radius: 4px;

    /* padding: 10px; */
    display: flex;
    align-items: center;
    background-color: #fff;

    :deep(.arco-textarea-word-limit) {
      right: 50px;
    }

    :deep(.arco-btn-icon) {
      z-index: 1002;
    }

    :deep(.arco-textarea) {
      padding: 10px 16px;
      margin-bottom: 24px;
      background-color: #fff;
      font-size: 12px;
      font-weight: 400;
    }
  }

  .send-icon {
    position: absolute;
    right: 4px;
    bottom: 2px;
    color: #1890ff;
    cursor: pointer;
  }

  .clear-icon {
    position: absolute;
    right: 28px;
    bottom: 2px;
    cursor: pointer;
  }
</style>
