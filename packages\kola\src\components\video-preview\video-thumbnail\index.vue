<template>
  <div @click="openPreview" class="thumbnail">
    <div class="material-mask"></div>
    <template v-if="coverUrl">
      <IconPlayCircleFill size="20" class="icon-play" v-if="videoSrc" />
      <a-image :src="coverUrl" :preview="false" class="pre-img"></a-image>
    </template>
    <template v-else>
      <IconPlayCircleFill size="30" class="icon-play" v-if="videoSrc" />
    </template>
  </div>
  <VideoPreview
    :src="videoSrc"
    v-model:previewVisible="previewVisible"
    :is-group="isGroup"
    :current-index="currentIndex"
    :videos="videos"
    v-if="previewVisible"
  />
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import VideoPreview from '../preview/index.vue';
  import { VideoThumbnailProps } from '../interface';

  const { videoSrc, coverUrl, isGroup, currentIndex, videos } =
    defineProps<VideoThumbnailProps>();
  const previewVisible = ref(false);

  const openPreview = () => {
    previewVisible.value = true;
  };
</script>

<style scoped lang="less">
  .thumbnail {
    height: 50px;
    width: 88px;
    position: relative;
    flex-shrink: 0;
    margin-right: 8px;
    margin-bottom: 8px;
    text-align: center;
    cursor: pointer;

    .material-mask {
      position: absolute;
      inset: 0;
      background-color: rgb(0 0 0 / 20%);
      opacity: 0;
    }

    &:hover {
      .material-mask {
        opacity: 1;
      }
    }

    .pre-img {
      width: 100%;
      height: 100%;

      &:deep(img) {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  .icon-play {
    cursor: pointer;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 99;
    transform: translate(-50%, -50%);
    background-color: var(--color-bg-1);
  }
</style>
