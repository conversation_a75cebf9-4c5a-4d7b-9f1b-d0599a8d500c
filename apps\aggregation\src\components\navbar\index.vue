<template>
  <div class="navbar">
    <Logo width="180px" />

    <a-menu
      mode="horizontal"
      :selected-keys="[activeKey]"
      @menu-item-click="handleMenuClick"
      class="menu"
      v-if="scene === 'system'"
    >
      <template v-for="item in menuItems" :key="item.path">
        <a-menu-item>
          <icon-component v-if="item.icon" :type="item.icon" />
          <span>{{ item.title }}</span>
        </a-menu-item>
      </template>
    </a-menu>
    <UserInfo v-if="scene === 'system'" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { generateMenuItems } from '@/utils/menu';
  import Logo from '@/assets/images/logo.svg?component';

  const router = useRouter();
  const route = useRoute();

  const props = defineProps<{
    scene: 'public' | 'system';
  }>();

  const isPublicRoute = computed(() => {
    return props.scene === 'public';
  });

  const menuItems = computed(() => {
    // 直接从路由模块获取一级路由
    const publicModules = import.meta.glob('@/router/routes/public/*.ts', {
      eager: true,
    });
    const systemModules = import.meta.glob('@/router/routes/system/*.ts', {
      eager: true,
    });

    const routes = isPublicRoute.value ? publicModules : systemModules;
    const firstLevelRoutes = [];

    Object.keys(routes).forEach((key) => {
      const defaultModule = routes[key].default;
      if (!defaultModule) return;
      const moduleList = Array.isArray(defaultModule)
        ? defaultModule
        : [defaultModule];
      firstLevelRoutes.push(...moduleList);
    });

    return generateMenuItems(firstLevelRoutes);
  });

  const activeKey = computed(() => route.matched?.[0].path);

  const handleMenuClick = (path: string) => {
    router.push(path);
  };

  function findLeftMenuItem(items: any[], path: string): any {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of items) {
      if (item.path === path) return item;
      if (item.children) {
        const found = findLeftMenuItem(item.children, path);
        if (found) return found;
      }
    }
    return null;
  }
</script>

<style scoped lang="less">
  .navbar {
    display: flex;
    align-items: center;

    .logo {
      margin-right: 40px;

      img {
        height: 32px;
      }
    }

    // :deep(.arco-menu-selected-label) {
    //   display: none;
    // }
    .menu,
    :deep(.arco-menu-item) {
      background: unset;
    }

    :deep(.arco-menu-item:hover) {
      background-color: rgb(42 85 229 / 8%);
    }

    :deep(
        .arco-menu-light.arco-menu-horizontal
          .arco-menu-item.arco-menu-selected:hover
      ) {
      background-color: rgb(42 85 229 / 8%);
    }
  }
</style>
