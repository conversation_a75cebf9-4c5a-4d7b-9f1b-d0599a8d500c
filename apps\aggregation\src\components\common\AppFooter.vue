<template>
  <div class="group_12 align-center flex-row justify-center">
    <span class="text">西安诚优网络科技有限责任公司版权所有</span>
    <span class="text">
      <a href="https://beian.miit.gov.cn/" target="_blank" class="link">
        陕ICP备16008755号-12
      </a>
    </span>
    <span class="text go-terms">
      <a :href="termsUrl" target="_blank" class="link">用户协议</a>
    </span>
    <span class="text go-terms">
      <a :href="privacyUrl" target="_blank" class="link">隐私政策</a>
    </span>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import useHomeConfigStore from '@/store/modules/home-config';

  const homeConfig = useHomeConfigStore();
  const termsUrl = computed(() => homeConfig.service_link);
  const privacyUrl = computed(() => homeConfig.privacy_link);
</script>

<style scoped lang="less">
  .group_12 {
    height: 86px;
    background: linear-gradient(180deg, #f0f7ff 0%, #e2efff 100%);
    gap: 24px;
  }

  .text {
    font-weight: 400;
    font-size: 14px;
    color: rgb(0 0 0 / 84%);
    line-height: 24px;
    text-align: left;
    font-style: normal;
    cursor: pointer;
  }

  .link {
    text-decoration: unset;
    color: rgb(0 0 0 / 84%);
  }
</style>
