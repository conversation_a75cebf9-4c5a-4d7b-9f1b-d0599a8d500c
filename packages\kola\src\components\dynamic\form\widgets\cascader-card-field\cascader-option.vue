<template>
  <li
    :class="{
      'arco-cascader-option': true,
      'arco-cascader-option-active': active,
      'show-value-option': props.isShowValue,
    }"
  >
    <a-checkbox
      v-if="isMultiple"
      :model-value="checkedStatus.checked"
      :indeterminate="checkedStatus.indeterminate"
      @change="(value: any, ev: Event) => {
            ev.stopPropagation();
            cascaderCtx.handlePathChange?.(option);
            cascaderCtx.onClickOption?.(
              option,
              !checkedStatus.checked
            )
          }"
      @click="(ev: Event) => ev.stopPropagation()"
    >
    </a-checkbox>
    <a-radio
      :model-value="radioStatus"
      v-else
      @change="
        (value: any, ev: Event) => {
          ev.stopPropagation();
            cascaderCtx.handlePathChange?.(option);
            cascaderCtx.onClickOption?.(
              option,
              !checkedStatus.checked
            )
        }
      "
      @click="(ev: Event) => ev.stopPropagation()"
    ></a-radio>
    <div
      class="arco-cascader-option-label"
      @click="() => cascaderCtx.handlePathChange?.(option)"
    >
      <a-tooltip position="bl" :disabled="option.label.length < 8">
        <template #content>
          <div class="tooltip-content">
            <p>{{ option.label }}</p>
            <p v-if="props.isShowValue">{{ option.value }}</p>
          </div>
        </template>
        <div>
          <div class="option-label">{{ option.label }}</div>
          <div class="option-value" v-if="props.isShowValue">{{
            option.value
          }}</div>
        </div>
      </a-tooltip>

      <icon-right v-if="!option.isLeaf" />
    </div>
  </li>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue';
  import { CascaderOptionInfo } from './interface';
  import { cascaderInjectionKey, CascaderContext } from './context';
  import { getCheckedStatus, getRadioStatus } from './utils';

  const props = defineProps<{
    option: CascaderOptionInfo;
    active: boolean;
    checkStrictly: boolean;
    searchOption: boolean;
    pathLabel: boolean;
    isMultiple: boolean;
    isShowValue?: boolean;
  }>();

  const emit = defineEmits(['loadingChange']);

  const cascaderCtx = inject<Partial<CascaderContext>>(
    cascaderInjectionKey,
    {}
  );

  const checkedStatus = computed(() => {
    return getCheckedStatus(props.option, cascaderCtx.valueMap);
  });

  const radioStatus = computed(() => {
    return getRadioStatus(props.option, cascaderCtx.valueMap);
  });
</script>

<style lang="less" scoped>
  .arco-cascader-option {
    .option-label,
    .option-value {
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
      padding-right: 12px;
    }

    .option-value {
      font-size: 13px;
    }

    .arco-cascader-option-label {
      max-width: 100%;
    }
  }

  .tooltip-content {
    p {
      margin: 0;
    }
  }

  .arco-cascader-option.show-value-option {
    height: auto;
    line-height: 22px;
    padding: 5px 0 5px 12px;
  }
</style>
