export type PageEvent =
  | {
      type: 'refreshTable';
      payload?: {
        resetPagination?: boolean;
        resetPageSize?: boolean;
      };
    }
  | {
      type: 'changeTableExpanded';
      payload: {
        isExpanded: boolean;
      };
    }
  | {
      type: 'updateTableData';
      payload: {
        data: any[];
      };
    }
  | {
      type: 'selectStrategy';
      payload: {
        data: any;
      };
    }
  | {
      type: 'setModalOkLoading';
      payload: {
        isLoading: boolean;
      };
    }
  | {
      type: 'setModalOkLoading';
      payload: {
        isLoading: boolean;
      };
    }
  | {
      type: 'customFieldsLoaded';
      payload: {
        sets: any[];
        configs: any[];
      };
    }
  | {
      type: 'formValidate';
      payload?: {
        data?: any;
      };
    };
