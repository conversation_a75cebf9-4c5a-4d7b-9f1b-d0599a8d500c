<template>
  <a-form
    :model="formData"
    layout="vertical"
    class="login-form"
    @submit="submitHandle"
    ref="currentFormRef"
  >
    <a-form-item
      field="phone"
      hide-asterisk
      :rules="[
        { required: true, message: '请输入手机号' },
        {
          validator(value, callback) {
            const reg = /^1[3-9]\d{9}$/;
            if (!reg.test(value)) {
              callback('请输入正确的手机号');
            }
            callback();
          },
        },
      ]"
      hide-label
    >
      <a-input
        v-model="formData.phone"
        placeholder="请输入手机号"
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="code"
      hide-label
      hide-asterisk
      :rules="[{ required: true, message: '请输入验证码' }]"
    >
      <a-input v-model="formData.code" hide-button placeholder="验证码">
        <template #suffix>
          <div class="verify-part">
            <a-button
              type="text"
              class="verify-btn"
              @click="sendCode"
              :disabled="verifyDisabled"
            >
              {{
                countdown > 0 ? `${countdown}s后重试` : '发送验证码'
              }}</a-button
            >
          </div>
        </template>
      </a-input>
    </a-form-item>
    <div class="login-form-footer">
      <a-form-item
        style="margin-bottom: 0"
        :rules="[
          {
            message: '请阅读并勾选协议',
            type: 'boolean',
            true: true,
          },
        ]"
        field="agree"
      >
        <a-checkbox v-model="formData.agree"> 我已阅读并同意 </a-checkbox>

        <a-link :href="serviceLink" target="_blank" rel="noopener noreferrer">
          <span>服务协议</span>
        </a-link>
        <div>和</div>
        <a-link :href="privacyLink" target="_blank" rel="noopener noreferrer">
          <span>隐私条款</span>
        </a-link>
      </a-form-item>
      <a-form-item style="margin-bottom: 0">
        <a-button type="primary" html-type="submit" long> 登录/注册 </a-button>
      </a-form-item>
    </div>
  </a-form>
  <VerifyModal
    v-model="verifyVisible"
    @confirm="confirmHandle"
    ref="verifyModalRef"
  />
</template>

<script setup lang="ts">
  import { computed, reactive, ref, useTemplateRef } from 'vue';
  import { FormInstance } from '@arco-design/web-vue';
  import { useRoute, useRouter } from 'vue-router';
  import useHomeStore from '@/store/modules/home-config';
  import commonApi from '@/api/common';
  import useUserStore from '@/store/modules/user';
  import VerifyModal from '../components/verify-modal.vue';
  import { LogInType } from '../type';

  const router = useRouter();
  const userStore = useUserStore();
  const verifyVisible = ref(false);
  const currentFormRef = ref<FormInstance>();
  const route = useRoute();
  const redirectUrl = route.query?.redirectUrl;
  const verifyModalRef = useTemplateRef('verifyModalRef');

  const timer = ref<number>();
  const countdown = ref(0);
  const COUNTDOWN_SECONDS = 60;

  const homeConfigStore = useHomeStore();
  const serviceLink = computed(() => {
    return homeConfigStore.homeConfig.service_link;
  });

  const privacyLink = computed(() => {
    return homeConfigStore.homeConfig.privacy_link;
  });

  const formData = reactive({
    phone: '',
    code: '',
    agree: false,
  });

  const verifyDisabled = computed(() => {
    const reg = /^1[3-9]\d{9}$/;
    return !reg.test(String(formData.phone)) || countdown.value > 0;
  });

  const sendCode = async () => {
    await currentFormRef.value?.validateField('phone');
    verifyVisible.value = true;
  };

  const confirmHandle = async (captchaParams: Record<string, any>) => {
    try {
      await commonApi.smsLogin({
        phone: String(formData.phone),
        ...captchaParams,
      });
      startCountdown(); // 开始倒计时
      verifyVisible.value = false;
    } catch (err: any) {
      // 图形验证码校验失败
      if (err?.code === 10006) {
        verifyModalRef.value?.captchaRefresh();
      } else {
        verifyVisible.value = false;
      }
    }
  };

  const startCountdown = () => {
    countdown.value = COUNTDOWN_SECONDS;
    clearInterval(timer.value);
    timer.value = window.setInterval(() => {
      countdown.value -= 1;
      if (countdown.value <= 0) {
        clearInterval(timer.value);
      }
    }, 1000);
  };

  const submitHandle = async () => {
    const res = await currentFormRef.value?.validate();
    if (res) return;
    try {
      await userStore.login({
        type: LogInType.sms,
        phone: String(formData.phone),
        code: String(formData.code),
      });
      if (redirectUrl) {
        router.push(redirectUrl as string);
      } else {
        router.push('/');
      }
    } catch (error) {
      //
    }
  };
</script>

<style scoped lang="less">
  .login-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 260px;
  }

  .login-form-footer {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: 0;
  }
</style>
