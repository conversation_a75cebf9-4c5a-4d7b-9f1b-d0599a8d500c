<template>
  <div class="operations">
    <a-space>
      <template v-for="item in operation" :key="item.text">
        <DynamicButton
          v-if="!item.right"
          :config="item"
          :filter="filter"
        ></DynamicButton>
      </template>
    </a-space>
    <a-space class="right-operation">
      <template v-for="item in operation" :key="item.text">
        <DynamicButton
          v-if="item.right"
          :config="item"
          :filter="filter"
        ></DynamicButton>
      </template>
      <!-- <a-button v-if="!hideResetColumnsWidth" type="text" @click="resetColumns">
        <template #icon>
          <a-tooltip content="重置列宽">
            <reset-icon :width="14" :height="14" fill="currentColor" />
          </a-tooltip>
        </template>
      </a-button> -->
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { defineOptions } from 'vue';
  import type { DButton } from '../../types/button';
  import ResetIcon from './svg/reset.svg?component';

  defineProps<{
    operation: DButton[];
    filter: any;
    hideResetColumnsWidth?: boolean;
    resetColumns: () => void;
  }>();
  defineOptions({ name: 'OperationBar' });
</script>

<style scoped lang="less">
  .operations {
    // margin-bottom: 16px;
    display: flex;
  }

  .right-operation {
    margin-left: auto;
  }
</style>
