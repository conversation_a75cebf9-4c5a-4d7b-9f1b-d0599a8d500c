import { LOGIN_URL } from '../constants';

// eslint-disable-next-line import/prefer-default-export
export const getLoginUrl = () => {
  removeCookie('token');
  return `${LOGIN_URL}?redirect=${encodeURIComponent(window.location.href)}`;
};

export function getCookie(name) {
  const reg = RegExp(`${name}=([^;]+)`);
  const arr = document.cookie.match(reg);
  if (arr) {
    return arr[1];
  }
  return '';
}

// @ts-ignore
const { MODE } = import.meta.env;
const isDev = MODE === 'development';
const isTest = MODE === 'testing';
const isProd = MODE === 'production';

// 测试链接
// const url1 = 'http://test-passport.cmywkj.cn';
// const url2 = 'http://test-adp.ghfkj.cn';
// const url3 = 'http://subdomain.example.com';
// const url4 = 'http://example.co.uk';
//
//  提取域名部分
// console.log(extractDomainFromUrl(url1));  输出: .cmywkj.cn
// console.log(extractDomainFromUrl(url2));  输出: .ghfkj.cn
// console.log(extractDomainFromUrl(url3));  输出: .example.com
// console.log(extractDomainFromUrl(url4));  输出: .co.uk
export function extractDomainFromUrl(url) {
  try {
    // 创建 URL 对象
    const urlObject = new URL(url);

    // 从 URL 对象中获取主机名
    const { hostname } = urlObject;

    // 使用正则表达式匹配所有域名后缀
    const regex = /(\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,})(?::\d+)?$/;
    const match = hostname.match(regex);

    return match ? match[1] : null;
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
}

export function setCookie(name, value, day) {
  const date = new Date();
  const domain = extractDomainFromUrl(window.location.origin);
  date.setDate(date.getDate() + day);
  document.cookie = `${name}=${value};expires=${date};Path=/;${
    isDev ? '' : `domain=${domain};`
  }`;
}

// 删除cookie
export function removeCookie(name) {
  setCookie(name, null, -1);
}
