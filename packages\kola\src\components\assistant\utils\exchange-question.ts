export const exchangeQuestion = (questionList: any[], currentIndex: number) => {
  const result: any[] = [];
  let index = currentIndex;
  if (questionList.length <= 5) {
    return {
      list: questionList,
      index,
    };
  }
  for (let i = 0; i < 5; i += 1) {
    if (questionList[index]) {
      result.push(questionList[index]);
    }
    index = (index + 1) % questionList.length;
  }
  return {
    list: result,
    index,
  };
};

export default exchangeQuestion;
