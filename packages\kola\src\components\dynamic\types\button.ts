import { Component } from 'vue';
import type { RenderContent } from '@arco-design/web-vue/es/_utils/types';
import { PageEvent } from './event';
import { DForm } from './form';
import { DDetail } from './detail';
import { Filter } from './table';

export type DButton = {
  text: string;
  props: {
    type?:
      | 'primary'
      | 'info'
      | 'success'
      | 'warning'
      | 'danger'
      | 'text'
      | 'outline';
    disabled?:
      | boolean
      | ((data: {
          filter: Filter;
          record: Record<string, any> | undefined;
          getCustomParams?: any;
          tableData?: any[];
        }) => boolean);
    loading?: boolean;
    id?: string;
  };
  visible?: (record?: Record<string, any> | undefined) => boolean;
  icon?: string;
  clickActionType: 'modal' | 'link' | 'batch' | 'drawer' | 'action';
  linkUrl?: string;
  tooltip?: string;
  stopPropagation?: boolean;
  right?: boolean;
  resizeColumns?: boolean;
  modal?: DButtonModal;
  auth?: string[] | string;
  operationPosition?: 'batch' | 'top' | 'left' | 'right'; // 控制操作按钮的展示位置
  action?: (data: {
    record?: Record<string, any> | undefined;
    refreshTable?: unknown;
    formData?: any;
    dispatch: (action: PageEvent) => void;
    tableData?: any[];
    filter: Filter;
  }) => void | Promise<void>;
};

export type DButtonModal = {
  props: {
    title?: string;
    width?: number | string;
    fullscreen?: boolean;
    footer?: boolean | RenderContent;
    bodyStyle?: any;
    drawerStyle?: any;
    renderToBody?: boolean;
    popupContainer?: string;
  };
  contentType: 'form' | 'custom' | 'text' | 'detail';
  form?: DForm;
  custom?: Component;
  text?: string;
  detail?: DDetail;
  getDefaultValue?: object | ((record?: any, getCustomParams?: any) => any);
  action?: (data: {
    record?: Record<string, any> | undefined;
    refreshTable?: unknown;
    formData?: any;
    dispatch: (action: PageEvent) => void;
    getCustomParams?: any;
    closePopover?: any;
  }) => Promise<void> | Promise<boolean> | Promise<boolean | void>;
  close?: (data: {
    record?: Record<string, any> | undefined;
    refreshTable?: unknown;
    formData?: any;
    dispatch: (action: PageEvent) => void;
    getCustomParams?: any;
  }) => void;
  alert?: {
    message: string;
  };
};
