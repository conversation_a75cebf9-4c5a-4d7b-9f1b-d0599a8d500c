<template>
  <div class="case-container">
    <div class="header">
      <div class="title"> 客户案例 </div>
      <div class="description"> AI驱动数字化转型的真实赋能实践 </div>
    </div>
    <section class="case">
      <div class="function-tabs">
        <div
          v-for="(item, index) in FUNCTION"
          :class="['function-tab', { active: curFunction === index }]"
          :key="index"
          @click="handleFunctionClick(index)"
        >
          <span>{{ item }}</span>
        </div>
      </div>
      <CaseCard style="margin-bottom: 60px" :case-list="showCaseList" />
    </section>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import CaseCard from '@/components/common/CaseCard.vue';
  import { getCaseList } from '@/api/case';

  const router = useRouter();

  const FUNCTION = ref<string[]>([]);
  const curFunction = ref(0);

  const allCaseList = ref<{ case_list: any[]; case_industry: string }[]>([]);

  // 如果curFunction为0，则显示全部
  const showCaseList = computed(() => {
    return allCaseList.value[curFunction.value]?.case_list || [];
  });

  const getCaseListData = async () => {
    const res = await getCaseList();
    allCaseList.value = res.data.list;
    FUNCTION.value = allCaseList.value.map((item: any) => item.case_industry);

    // 根据路由参数industry，设置curFunction
    const { industry } = router.currentRoute.value.query;

    if (industry) {
      curFunction.value = FUNCTION.value.indexOf(industry as string);
    }
  };

  const handleFunctionClick = (index: number) => {
    curFunction.value = index;
    // 修改路由参数
    router.replace({
      query: {
        industry: FUNCTION.value[index],
      },
    });
  };

  onMounted(() => {
    getCaseListData();
  });
</script>

<style scoped lang="less">
  .case {
    max-width: 1520px;
    padding: 0 40px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
  }

  .header {
    width: 100%;
    height: 232px;
    background: #ebf4ff;
    color: rgb(0 0 0 / 84%);
    font-weight: 400;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      height: 36px;
      margin-bottom: 16px;
      font-weight: 500;
      font-size: 36px;
      line-height: 36px;
    }

    .description {
      height: 36px;
      font-size: 18px;
      line-height: 36px;
    }
  }

  .function-tabs {
    display: flex;
    gap: 12px;
    margin: 32px 0;
    width: 980px;
    height: 40px;

    .function-tab {
      width: 112px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      cursor: pointer;
      transition: color 0.2s;
      border-radius: 8px;
      background: #fff;
      color: rgb(0 0 0 / 84%);
    }

    .function-tab.active {
      background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
      color: #fff;
    }
  }
</style>
