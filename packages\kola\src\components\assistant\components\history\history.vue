<template>
  <div class="mask" id="history">
    <div class="drag-handle" @mousedown="startDragging">聊天记录</div>
    <feedback ref="feedbackRef" />
    <a-input
      class="search-input"
      v-model="searchValue"
      placeholder="请输入内容"
      allow-clear
      @keydown="handleKeyDown"
    />
    <a-tooltip content="返回" popup-container="#history">
      <a-button
        @click="handleBack"
        class="back-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-left size="14" />
        </template>
      </a-button>
    </a-tooltip>
    <a-checkbox v-if="canChoose" class="checkbox" v-model="checkedAll"
      >全选</a-checkbox
    >
    <a-button
      v-if="!canChoose"
      @click="handleChoose"
      class="choose-button"
      type="outline"
      size="mini"
    >
      选择
    </a-button>
    <a-button
      v-if="canChoose"
      @click="handleCancel"
      class="choose-button"
      type="outline"
      size="mini"
    >
      取消
    </a-button>
    <a-tooltip content="关闭" popup-container="#history">
      <a-button
        @click="handleClose"
        class="close-button"
        type="outline"
        size="mini"
      >
        <template #icon>
          <icon-close size="13" />
        </template>
      </a-button>
    </a-tooltip>
    <div class="scroll-wrapper" ref="scrollWrapper" @scroll="handleScroll">
      <record-message
        v-for="item in chatRecord"
        :key="item.id"
        :message="item.message"
        :type="item.type"
        :time="item.createdAt"
        :can-choose="canChoose"
        :check-list="checkList"
        :id="item.id"
        :search-text="searchedValue"
        @check-add="checkAdd"
        @check-delete="checkDelete"
      />
    </div>
    <div class="operation" v-if="canChoose">
      <a-spin v-if="loading" />
      <span v-else class="delete" @click="handleDelete">删除</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { inject, Ref, ref, watch, onMounted, nextTick } from 'vue';
  import { useDragging } from '../../utils/dragging';
  import Feedback from '../feedback.vue';
  import RecordMessage from './record-message.vue';
  import useChatRecord from '../../hooks/use-chat-record';
  import websocketApi from '../../apis';
  import { generateRequestId } from '../../utils/uuid';

  const emit = defineEmits(['close', 'back']);
  const { chatRecord, queryChatRecord, resetRecord, nextPage } =
    useChatRecord();
  const chartWrapper = inject<Ref<HTMLElement | null | undefined> | undefined>(
    'chartWrapper'
  );
  const loading = ref(false);
  const canChoose = ref(false);
  const checkedAll = ref(false);
  const searchValue = ref('');
  const searchedValue = ref('');
  const checkList = ref<any[]>([]);
  const feedbackRef = ref<HTMLElement | null>(null);
  const scrollWrapper = ref<HTMLElement | null>(null);
  const { startDragging } = useDragging(chartWrapper);

  const handleClose = () => {
    emit('close');
  };

  const handleBack = () => {
    emit('back');
  };
  const handleChoose = () => {
    canChoose.value = true;
  };
  const handleCancel = () => {
    canChoose.value = false;
  };
  const handleSearch = (value: string) => {
    searchedValue.value = value;
    resetRecord();
    queryChatRecord({ keyword: value }, () =>
      nextTick(() => {
        if (scrollWrapper.value) {
          scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
        }
      })
    );
  };
  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSearch(searchValue.value);
    }
  };

  const checkAdd = (id: string) => {
    checkList.value.push(id);
  };
  const checkDelete = (id: string) => {
    checkList.value = checkList.value.filter((item: any) => item !== id);
  };
  const handleDelete = () => {
    if (checkList.value.length === 0) {
      return;
    }
    const uuid = generateRequestId();
    loading.value = true;
    websocketApi.send({
      messageType: 'onDeleteHistoryMessages',
      requestId: uuid,
      data: {
        ids: checkList.value,
      },
    });
    websocketApi.on('onDeleteHistoryMessages', (res) => {
      const { code, requestId } = res;
      if (code === 0 && requestId === uuid) {
        // @ts-ignore
        feedbackRef?.value?.show('删除成功', 'success', 2000);
        checkList.value = [];
        checkedAll.value = false;
        resetRecord();
        queryChatRecord({ keyword: searchValue.value }, () =>
          nextTick(() => {
            if (scrollWrapper.value) {
              scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
            }
          })
        );
      }
      loading.value = false;
    });
  };
  watch(checkedAll, (value) => {
    if (value) {
      checkList.value = chatRecord.value.map((item) => item.id);
    } else {
      checkList.value = [];
    }
  });
  const handleScroll = () => {
    if (scrollWrapper.value?.scrollTop === 0) {
      const previousHeight = scrollWrapper.value.scrollHeight;
      nextPage(() =>
        nextTick(() => {
          if (scrollWrapper.value) {
            const newHeight = scrollWrapper.value.scrollHeight;
            scrollWrapper.value.scrollTop = newHeight - previousHeight;
          }
        })
      );
    }
  };

  watch(
    chatRecord,
    (value) => {
      if (value.length > 0) {
        nextTick(() => {
          if (scrollWrapper.value) {
            scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
          }
        });
      }
    },
    { once: true, immediate: true }
  );
</script>

<style scoped>
  .drag-handle {
    cursor: move;
    padding: 18px 23px 18px 30px;
    padding-bottom: 10px;
    user-select: none;
    width: calc(100% - 108px);
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000;
  }

  .close-button {
    position: absolute;
    top: 18px;
    right: 18px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .close-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .choose-button {
    position: absolute;
    top: 18px;
    right: 44px;
    background: none;
    border: none;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
    font-size: 12px;
  }

  .checkbox {
    position: absolute;
    top: 23px;
    right: 94px;
    font-size: 12px;
  }

  .choose-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .back-button {
    position: absolute;
    top: 16px;
    left: 10px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    color: rgb(75 75 75);
    border-color: rgb(233 233 233);
  }

  .back-button:hover {
    color: rgb(var(--link-4));
    border-color: rgb(var(--primary-3));
  }

  .scroll-wrapper {
    overflow-y: auto;
    width: 100%;
    height: calc(100% - 100px);
    padding: 0 23px 40px;
    margin-top: 10px;
  }

  :deep(.arco-checkbox-label) {
    font-size: 12px;
    color: rgb(75 75 75);
    margin-left: 4px;
  }

  .search-input {
    width: 573px;
    height: 32px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d8d8d8;
    margin-left: 24px;
  }

  .operation {
    position: absolute;
    bottom: -20px;
    width: 100%;
    height: 48px;
    text-align: center;
    color: #f00;
    background: #fff;
    border-radius: 0 0 8px 8px;
    border: 1px solid #d8d8d8;
    display: flex;
    justify-content: center;
    align-items: center;

    .delete {
      cursor: pointer;
    }
  }
</style>
