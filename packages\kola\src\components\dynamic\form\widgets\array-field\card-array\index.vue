<!-- eslint-disable vue/valid-v-for -->
<template>
  <CollapseCard :title="field.label">
    <template #extra>
      <a-button
        @click.stop="handleAdd(value.length - 1)"
        class="add-btn"
        type="outline"
        :disabled="!canAdd"
      >
        新增{{ field.childItemLabel }}
      </a-button>
    </template>
    <div
      v-for="(item, index) in value"
      :key="item[field.rowKey] || getUniqueId()"
      class="array-item"
    >
      <CollapseCard
        :title="`${field.childItemLabel}（${index + 1}）`"
        :title-style="titleStyle"
      >
        <WidgetByType
          :field="field.item"
          :path="`${path}[${index}]`"
          v-model="value[index]"
          :hide-label="true"
        />
        <template #extra>
          <a-space>
            <a-button
              @click.stop="handleCopy(index)"
              class="copy-btn"
              type="text"
              v-if="field.canCopy"
            >
              <template #icon>
                <icon-copy />
              </template>
            </a-button>

            <a-button
              @click.stop="handleRemove(index)"
              class="remove-btn"
              type="text"
              :disabled="!canRemove"
            >
              <template #icon>
                <icon-delete />
              </template>
            </a-button>
          </a-space>
        </template>
      </CollapseCard>
    </div>
  </CollapseCard>
</template>

<script setup lang="ts">
  import { uniqueId } from 'lodash';
  import { ArrayField } from '../../../../types/form';
  import WidgetByType from '../../widget-by-type.vue';
  import CollapseCard from '../../collapse-card/index.vue';

  const value = defineModel<any[]>({ default: [] });
  const titleStyle = {
    backgroundColor: 'rgba(0,0,0,0.04)',
  };
  defineProps<{
    field: ArrayField;
    path: string;
    canAdd: boolean;
    canRemove: boolean;
  }>();

  function getUniqueId() {
    return uniqueId('card');
  }

  const emits = defineEmits<{
    add: [index: number];
    remove: [index: number];
    copy: [index: number];
  }>();

  const handleAdd = (index: number) => {
    emits('add', index);
  };

  const handleRemove = (index: number) => {
    emits('remove', index);
  };

  const handleCopy = (index: number) => {
    emits('copy', index);
  };
</script>

<style scoped lang="less">
  .array-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .add-btn {
    margin-left: 8px;
  }

  .remove-btn {
    color: var(--color-text-2);
    margin-left: 8px;
  }
</style>
