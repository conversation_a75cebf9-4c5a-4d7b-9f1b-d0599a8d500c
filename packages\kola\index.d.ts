import type { App } from 'vue';
import {
  DForm,
  formSchema,
  FormData,
} from './src/components/dynamic/types/form';

declare const Kola: {
  DynamicForm: import('vue').DefineComponent<
    {
      formSchema: {
        type: import('vue').PropType<formSchema>;
      };
      defaultFormData: {
        type: import('vue').PropType<FormData>;
      };
      resetFormData: {
        type: import('vue').PropType<FormData>;
      };
      disabled: {
        type: BooleanConstructor;
      };
    },
    {
      prefixCls: string;
    },
    unknown,
    object,
    object,
    import('vue').ComponentOptionsMixin,
    import('vue').ComponentOptionsMixin,
    Record<string, any>,
    string,
    import('vue').VNodeProps &
      import('vue').AllowedComponentProps &
      import('vue').ComponentCustomProps,
    Readonly<DForm>,
    object
  >;
};
export default Kola;
