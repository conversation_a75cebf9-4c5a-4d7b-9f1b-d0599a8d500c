<template>
  <a-menu
    mode="vertical"
    :selected-keys="[activeKey]"
    :default-open-keys="openKeys"
    @menu-item-click="handleMenuClick"
    show-collapse-button
    class="side-menu"
    style="width: 200px"
  >
    <template v-for="item in menuItems">
      <a-sub-menu v-if="item.children?.length" :key="item.key">
        <template #title>
          <component :is="item.icon" />
          <span>{{ item.title }}</span>
        </template>
        <a-menu-item v-for="child in item.children" :key="child.key">
          <component :is="child.icon" />

          <span>{{ child.title }}</span>
        </a-menu-item>
      </a-sub-menu>
      <a-menu-item v-else :key="item.key">
        <component :is="item.icon" />
        <span>{{ item.title }}</span>
      </a-menu-item>
    </template>
  </a-menu>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { generateMenuItems } from '@/utils/menu';

  const router = useRouter();
  const route = useRoute();
  const activeKey = computed(() => route.name as string);

  const menuItems = computed(() => {
    const matchRoutes = route.matched[0].children;

    return generateMenuItems(matchRoutes);
  });

  // 获取当前展开的子菜单
  const openKeys = computed(() => {
    const currentRoute = route.matched;
    return currentRoute.map((r) => r.name as string);
  });

  const handleMenuClick = (key: string) => {
    const item = findMenuItem(menuItems.value, key);
    if (item) {
      router.push(item.path);
    }
  };

  function findMenuItem(items: any[], key: string): any {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of items) {
      if (item.key === key) return item;
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  }
</script>

<style scoped lang="less">
  .side-menu {
    height: 100%;
    background-color: transparent;
    padding-top: 24px;

    :deep(.arco-menu-item) {
      background-color: unset;
    }

    :deep(.arco-menu-inline) {
      .arco-menu-inline-header {
        background-color: transparent;
      }
    }

    :deep(.arco-menu-selected),
    :deep(.arco-menu-item:hover) {
      background-color: #fff;
      border-radius: 8px;
    }
  }
</style>
