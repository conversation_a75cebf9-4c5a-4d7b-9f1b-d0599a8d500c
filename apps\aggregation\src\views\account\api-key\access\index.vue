<template>
  <div class="access-container">
    <div class="access-title">1. 选择应用</div>
    <div class="access-desc">
      请按如下方式设置 API Key 作为环境变量，选择应用后参考如下示例
    </div>
    <div class="access-content">
      <a-select
        v-model="selectedApp"
        placeholder="请选择应用"
        allow-clear
        allow-search
        name="store_app_id"
        :options="appOptions"
        style="width: 320px"
        :virtual-list-props="{ height: 150 }"
      >
      </a-select>
    </div>

    <div class="access-title">2. 选择示例代码</div>
    <div class="access-desc">请参考如下示例代码进行调用</div>
    <CopyableCodeBlock language="bash" :code="curlCode" />
  </div>
</template>

<script setup lang="tsx">
  import { onMounted, ref, watch } from 'vue';
  import userKeyApi from '@/api/user-key';
  import storeAppApi from '@/api/store';
  import CopyableCodeBlock from './CopyableCodeBlock.vue';

  const selectedApp = ref('');
  const appOptions = ref([]);

  const { apiKey } = defineProps<{
    apiKey: string;
  }>();

  const curlCode = ref(
    'curl -X POST --location "https://test-ai.smartroi.cn/api/v1/case/list" \\\n    -H "Content-Type: application/json" \\\n    -d \'{}\''
  );

  watch(selectedApp, async (newVal) => {
    if (newVal) {
      const res = await userKeyApi.access({
        id: apiKey,
        store_app_id: newVal,
      });
      curlCode.value = res.data?.curl || '';
    }
  });

  onMounted(() => {
    storeAppApi
      .list({
        limit: 10,
        page: 1,
        qry_cat_ids: [],
        qry_cat_typ: '',
        qry_acc_status: [1],
        search: '',
        sort_field: 'sales',
        sort_type: 'desc',
      })
      .then((res) => {
        appOptions.value = res.data?.list.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
  });
</script>

<style scoped lang="less">
  .access-container {
    width: 100%;

    :deep(.arco-btn-secondary) {
      background: #fff;
    }
  }

  .access-title {
    font-size: 16px;
    font-weight: 500;
    color: rgb(0 0 0 / 84%);
    line-height: 22px;
  }

  .access-content {
    display: flex;
    align-items: center;
    background: #f6f8fa;
    border-radius: 8px;
    padding: 16px 24px;
    margin-bottom: 16px;
  }

  .access-desc {
    font-size: 14px;
    color: rgb(0 0 0 / 48%);
    margin: 16px 0;
  }
</style>
