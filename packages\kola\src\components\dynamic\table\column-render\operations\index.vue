<template>
  <div class="table-operation-column">
    <DynamicButton
      :config="item"
      :key="item.customKey"
      :record="props.record"
      v-for="item in operationList?.slice(0, operationCount)"
    ></DynamicButton>
    <a-dropdown
      position="bottom"
      v-if="isNil(showOperationCount) && operationList?.length > 2"
      trigger="hover"
    >
      <a-button type="text">
        <template #icon>
          <icon-more />
        </template>
      </a-button>
      <template #content>
        <a-doption
          :key="item.customKey"
          v-for="item in operationList.slice(2, operationList.length)"
        >
          <DynamicButton
            :config="item"
            :key="item.customKey"
            :record="props.record"
          />
        </a-doption>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
  import { computed, defineProps } from 'vue';
  import { isBoolean, isFunction, isNil, uniqueId } from 'lodash';
  import type { DButton } from '../../../types/button';
  import { RenderProps } from '../type';

  const props = defineProps<
    RenderProps & {
      operations:
        | (DButton & { isShow?: (record: any) => boolean })[]
        | ((record: any) => any);
      showOperationCount?: number;
    }
  >();

  const operationList = computed(() => {
    const newOperation = isFunction(props.operations)
      ? props.operations(props.record)
      : props.operations;
    newOperation.forEach((item) => {
      item.customKey = uniqueId('btn');
    });

    return newOperation.filter((operation) => {
      if (isFunction(operation.isShow)) {
        return operation.isShow(props.record);
      }

      if (isBoolean(operation.isShow)) {
        return operation.isShow;
      }

      return true;
    });
  });

  const operationCount = props.showOperationCount ?? 2;
</script>

<style scoped lang="less">
  .table-operation-column {
    margin-left: -8px;
  }

  .table-operation-column-button {
    padding: 0 8px;
    font-size: 13px;
  }
</style>
