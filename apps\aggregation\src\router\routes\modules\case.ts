import { AppRouteRecordRaw } from '../types';
import { PUBLIC_LAYOUT } from '../base';

const APP_MARKET: AppRouteRecordRaw[] = [
  {
    path: '/case',
    name: 'case',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/case/index.vue'),
    },
    meta: {
      title: '案例',
      requiresAuth: false,
      order: 0,
    },
  },
  {
    path: '/case/detail/:id',
    name: 'caseDetail',
    components: {
      default: PUBLIC_LAYOUT,
      content: () => import('@/views/case/detail/index.vue'),
    },
    meta: {
      title: '案例详情',
      requiresAuth: false,
      order: 0,
    },
  },
];

export default APP_MARKET;
