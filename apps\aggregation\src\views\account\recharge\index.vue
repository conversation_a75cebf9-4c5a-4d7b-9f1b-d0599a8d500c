<template>
  <AccountPage :table="tableConfig">
    <div class="balance">
      <div class="balance-title">可用余额</div>
      <div class="balance-amount">{{ balance }}</div>
    </div>
  </AccountPage>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import tradeApi from '@/api/trade';
  import AccountPage from '../components/account-page/index.vue';

  // eslint-disable-next-line no-shadow
  enum PAY_TYPE {
    ALIPAY = '支付宝',
    WECHAT = '微信',
    SYSTEM = '系统充值',
  }

  const balance = ref(0);

  const tableConfig = {
    columns: [
      {
        title: '充值ID',
        dataIndex: 'order_id',
        width: 150,
      },
      {
        title: '用户名',
        dataIndex: 'order_user',
        width: 100,
      },
      {
        title: '充值金额',
        dataIndex: 'pay_amt',
        width: 100,
      },
      {
        title: '到账U点',
        dataIndex: 'order_score',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'order_status_text',
        width: 100,
      },
      {
        title: '充值方式',
        dataIndex: 'pay_type',
        width: 100,
        render: ({ record }) => {
          return PAY_TYPE[record.pay_type];
        },
      },
      {
        title: '充值时间',
        dataIndex: 'pay_time',
        width: 100,
      },
      {
        title: '备注',
        dataIndex: 'order_remark',
        width: 120,
      },
    ],

    load: {
      action: async (_filter, pageInfo) => {
        const pageParams = {
          page: pageInfo.pageNum,
          limit: pageInfo.pageSize,
        };
        const res = await tradeApi.incomingList(pageParams);

        return {
          data: {
            list: res.data?.list || [],
            total: res.data?.total || 0,
          },
        };
      },
    },
  };

  onMounted(() => {
    tradeApi.stats().then((res) => {
      balance.value = res.data?.balance || 0;
    });
  });
</script>

<style scoped lang="less">
  .balance {
    width: 100%;
    height: 135px;
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    margin: 24px 0;
  }

  .balance-title {
    font-size: 12px;
    color: rgb(0 0 0 / 68%);
    line-height: 16px;
  }

  .balance-amount {
    font-weight: 500;
    font-size: 34px;
    color: #ff7d00;
    line-height: 1;
    margin-top: 16px;
  }
</style>
