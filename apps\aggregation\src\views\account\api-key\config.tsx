import { ref } from 'vue';
import { Modal } from '@arco-design/web-vue';
import userKeyApi from '@/api/user-key';

const formSchema = {
  fields: [
    {
      label: 'APIKey名称',
      name: 'search',
      type: 'text',
      allowClear: true,
      span: 8,
    },
    // {
    //   label: '应用',
    //   name: 'filter_app_ids',
    //   span: 6,
    //   type: 'select',
    //   format: 'multipleSelect',
    //   select: {
    //     allowClear: true,
    //   },
    //   source: {
    //     data: async () => {
    //       const res = await userAppApi.list({
    //         page: 1,
    //         limit: 1000,
    //       });
    //       return (
    //         res.data?.list.map((item) => ({
    //           value: item.id,
    //           label: item.name,
    //         })) || []
    //       );
    //     },
    //   },
    // },
    {
      label: '状态',
      name: 'filter_statuses',
      type: 'select',
      format: 'multipleSelect',
      span: 6,
      select: {
        allowClear: true,
      },
      source: {
        data: [
          { value: 1, label: '启用' },
          { value: 0, label: '禁用' },
        ],
      },
    },
  ],
};

const getTableConfig = (actionCb) => {
  const editKeyConfig = createApiKeyConfig('edit');
  return {
    columns: [
      {
        title: 'APIKey名称',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: 'APIKey',
        dataIndex: 'id',
        width: 280,
        render: ({ record }) => {
          const isVisible = ref(false);
          const toggleVisibility = () => {
            isVisible.value = !isVisible.value;
          };

          const apiKey = record.id;
          const maskedKey =
            apiKey.length > 8
              ? apiKey.slice(0, 4) +
                '*'.repeat(apiKey.length - 8) +
                apiKey.slice(-4)
              : '*'.repeat(apiKey.length);

          return (
            <a-space align="center">
              <div>{isVisible.value ? apiKey : maskedKey}</div>
              {!isVisible.value ? (
                <icon-eye onClick={toggleVisibility} />
              ) : (
                <icon-eye-invisible onClick={toggleVisibility} />
              )}
            </a-space>
          );
        },
      },
      {
        title: 'Token',
        dataIndex: 'token',
        width: 280,
        render: ({ record }) => {
          const isVisible = ref(false);
          const toggleVisibility = () => {
            isVisible.value = !isVisible.value;
          };

          const apiKey = record.token;
          const maskedKey =
            apiKey.length > 8
              ? apiKey.slice(0, 4) +
                '*'.repeat(apiKey.length - 8) +
                apiKey.slice(-4)
              : '*'.repeat(apiKey.length);

          return (
            <a-space align="center">
              <div>{isVisible.value ? apiKey : maskedKey}</div>
              {!isVisible.value ? (
                <icon-eye onClick={toggleVisibility} />
              ) : (
                <icon-eye-invisible onClick={toggleVisibility} />
              )}
            </a-space>
          );
        },
      },

      {
        title: '状态',
        dataIndex: 'status_text',
        width: 100,
      },
      // {
      //   title: '权限',
      //   dataIndex: 'priv_apps',
      //   width: 100,
      //   render: ({ record }) => {
      //     return record.priv_apps.map((item) => item.name).join(',');
      //   },
      // },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        width: 180,
      },
      {
        title: '创建人',
        dataIndex: 'opt_user',
        width: 150,
      },

      {
        title: '操作',
        dataIndex: 'operation',
        width: 280,
        minWidth: 280,
        customRender: {
          type: 'operations',
          props: {
            showOperationCount: 4,

            operations: [
              // (record) => createApiKeyConfig('edit', record),
              editKeyConfig,
              {
                text: '去接入',
                props: {
                  type: 'text',
                },
                clickActionType: 'action',
                action: ({ record }) => {
                  actionCb?.(record.id);
                },
              },
              {
                isShow: (record) => {
                  return record.status === 1;
                },
                text: '禁用',
                props: {
                  type: 'text',
                },
                clickActionType: 'action',
                action: ({ record, refreshTable }) => {
                  userKeyApi
                    .status({
                      id: record.id,
                      status: 0,
                    })
                    .then(() => {
                      refreshTable();
                    });
                },
              },
              {
                isShow: (record) => {
                  return record.status === 0;
                },
                text: '启用',
                props: {
                  type: 'text',
                },
                clickActionType: 'action',
                action: ({ record, refreshTable }) => {
                  userKeyApi
                    .status({
                      id: record.id,
                      status: 1,
                    })
                    .then(() => {
                      refreshTable();
                    });
                },
              },
              {
                text: '删除',
                props: {
                  type: 'text',
                },
                clickActionType: 'action',
                action: ({ record, refreshTable }) => {
                  Modal.confirm({
                    title: '提示',
                    content: '确定删除该APIKey吗？',
                    onOk: () => {
                      userKeyApi.delete({ id: record.id }).then(() => {
                        refreshTable();
                      });
                    },
                  });
                },
              },
            ],
          },
        },
      },
    ],
  };
};

const createApiKeyConfig = (type: string) => {
  return {
    text: type === 'create' ? '创建APIKey' : '编辑',
    props: {
      type: type === 'create' ? 'primary' : 'text',
    },
    clickActionType: 'modal',
    auth: [],
    modal: {
      props: {
        title: '创建APIKey',
        width: 600,
        fullscreen: false,
      },
      contentType: 'form',
      getDefaultValue: (record) => {
        if (type === 'create') {
          return {
            name: '',
            priv_apps: [],
          };
        }

        return {
          id: record?.id,
          name: record?.name,
          priv_apps: [],
        };
      },
      form: {
        formSchema: {
          fields: [
            {
              label: 'APIKey名称',
              name: 'name',
              type: 'text',
              required: true,
              maxLength: 100,
            },
            // {
            //   label: '权限',
            //   name: 'priv_apps',
            //   type: 'select',
            //   format: 'multipleSelect',
            //   source: {
            //     valueKey: 'id',
            //     data: async () => {
            //       const res = await userAppApi.list({
            //         page: 1,
            //         limit: 1000,
            //       });
            //       return (
            //         res.data?.list.map((item) => ({
            //           value: item,
            //           id: item.id,
            //           label: item.name,
            //         })) || []
            //       );
            //     },
            //   },
            //   required: true,
            // },
          ],
        },
      },
      action: async ({ formData, refreshTable }) => {
        const res = await userKeyApi.save(formData);
        refreshTable();
        return res;
      },
    },
  };
};

export { formSchema, getTableConfig, createApiKeyConfig };
