<template>
  <div
    :class="{ 'collapse-card-active': isActive, 'collapse-card': true }"
    :style="style || {}"
  >
    <div class="collapse-card-header" role="button" :style="titleStyle || {}">
      <div class="collapse-card-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <!-- 描述 -->
      <div class="collapse-card-description">
        <slot name="description">{{ description }}</slot>
      </div>
      <div class="collapse-card-extra">
        <slot name="extra"></slot>
      </div>
      <div class="collapse-card-icon-hove" @click="isActive = !isActive">
        <icon-caret-right class="collapse-card-icon" />
      </div>
    </div>

    <div class="collapse-card-content" :style="contentStyle || {}">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { inject, ref } from 'vue';
  import useRegisterEvent from '../../../hooks/register-event';

  defineProps<{
    title?: string;
    style?: Record<string, any>;
    titleStyle?: Record<string, any>;
    contentStyle?: Record<string, any>;
    description?: string;
  }>();

  const isActive = ref(true);
  const formId = inject<string>('formId');

  useRegisterEvent(
    'formValidate',
    () => {
      isActive.value = true;
    },
    formId
  );
</script>

<style scoped lang="less">
  .collapse-card {
    overflow: hidden;
    line-height: 1.5715;
    border: 1px solid var(--color-neutral-3);
    border-radius: var(--border-radius-medium);

    .collapse-card-content {
      height: 0;
      padding: 0;
    }

    .collapse-card-icon {
      transition: transform 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
      color: var(--color-neutral-7);
    }
  }

  .collapse-card-active {
    > .collapse-card-header {
      background-color: var(--color-bg-2);
      border-color: var(--color-neutral-3);
      transition: border-color 0s ease 0s;

      > .collapse-card-icon-hove {
        > .collapse-card-icon {
          transform: rotate(90deg);
        }
      }
    }

    > .collapse-card-content {
      display: block;
      height: auto;
      padding: 16px;
    }
  }

  .collapse-card-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    overflow: hidden;
    color: var(--color-text-1);
    font-size: 14px;
    line-height: 24px;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid transparent;
    cursor: pointer;
    transition: border-color 0s ease 0.19s;
    padding: 8px 34px 8px 13px;
    gap: 10px;
    min-height: 49px;

    .collapse-card-description {
      flex: 1;
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: rgb(0 0 0 / 68%);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  .collapse-card-icon-hove {
    position: absolute;
    top: 50%;
    right: 13px;
    text-align: center;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    border-radius: 50%;
    justify-content: center;

    &:hover {
      background-color: var(--color-fill-2);
    }
  }

  .collapse-card-content {
    padding: 16px;
  }

  .collapse-card-extra {
    float: right;
    margin-right: 10px;
  }
</style>
