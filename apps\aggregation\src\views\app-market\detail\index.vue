<template>
  <section class="app-detail">
    <a-breadcrumb style="margin: 24px 0">
      <template #separator>
        <icon-right />
      </template>
      <a-breadcrumb-item
        v-for="(item, index) in capBreadcrumb"
        :key="item.value"
        @click="handleBreadcrumbClick(item.value, index)"
        >{{ item.label }}</a-breadcrumb-item
      >
    </a-breadcrumb>
    <div class="content">
      <div class="content-left flex-col shadow">
        <div class="card">
          <div class="card-header">
            <img class="avatar" :src="appInfo.logo" alt="头像" />
            <span class="title">{{ appInfo.name }}</span>
          </div>
          <TextOverflowTooltip
            :text="appInfo.desc"
            :lines="2"
            class-name="description"
          />
          <div class="card-actions">
            <span class="price">
              <span class="price-text">{{ appInfo.price_val }}</span>
              <span class="price-unit">U点/次</span>
            </span>
            <button class="detail-btn">成交量：{{ appInfo.sales }}</button>
          </div>
          <div class="create-btn-group">
            <a-button
              type="primary"
              class="create-btn"
              :style="{
                background:
                  appInfo.access_status === AppStatus.UNLISTED ? '#ccc' : '',
              }"
              :disabled="appInfo.access_status === AppStatus.UNLISTED"
              v-login="`/app/detail/${id}`"
              @click="handleAccessApp"
              >{{
                appInfo.access_status === AppStatus.LISTED
                  ? '应用接入'
                  : '敬请期待'
              }}</a-button
            >
            <a-button
              type="primary"
              class="create-btn"
              v-login="`/app/detail/${id}`"
              @click="handlePreviewApp"
              v-if="appInfo.preview_link"
              >立即体验</a-button
            >
          </div>
        </div>
        <ApiDoc
          v-if="appInfo.access_status === AppStatus.LISTED"
          :access-info="appInfo.access_info as any"
        />
        <!-- 应用详情 -->
        <a-card
          title="应用详情"
          :bordered="false"
          :style="{
            width: '100%',
          }"
        >
          <a-image
            v-for="image in appInfo.images"
            :key="image"
            :src="image"
            :alt="appInfo.name"
            :width="'100%'"
            :height="'100%'"
            :loading="
              appInfo.access_status === AppStatus.LISTED ? 'lazy' : 'eager'
            "
          />
        </a-card>
      </div>
      <div class="content-right">
        <a-card
          :style="{ borderRadius: '8px', width: '100%' }"
          class="shadow"
          :bordered="false"
          :header-style="{
            padding: '0 24px',
            height: '55px',
          }"
        >
          <template #title>
            <span style="font-weight: 500; font-size: 20px">热门应用</span>
          </template>
          <div
            class="popular-app"
            v-for="(app, index) in popularApps"
            :key="app.id"
            @click="handlePopularAppClick(app.id)"
          >
            <div class="info">
              <img :src="app.logo" alt="应用图片" />
              <span>{{ app.name }}</span>
            </div>
            <TextOverflowTooltip :text="app.desc" :lines="2" class-name="des" />
            <a-divider v-if="index !== popularApps.length - 1" />
          </div>
        </a-card>
      </div>
    </div>
  </section>
</template>

<script setup lang="tsx">
  import { computed, onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { Modal } from '@arco-design/web-vue';
  import storeAppApi from '@/api/store';
  import TextOverflowTooltip from '@/components/common/TextOverflowTooltip.vue';
  import ApiDoc from '../components/api-doc.vue';
  import { AppStatus, PopularAppType } from './type';

  const router = useRouter();
  // eslint-disable-next-line no-shadow
  enum PreviewType {
    tab = 'TAB',
    iframe = 'IFRAME',
  }

  const { id } = router.currentRoute.value.params;
  const defaultAppInfo = {
    logo: '',
    name: '',
    desc: '',
    price_val: 0,
    sales: 0,
    service_link: '',
    privacy_link: '',
    images: [],
    cap_pri_cat_id: 0,
    cap_pri_cat_name: '',
    cap_sec_cat_id: 0,
    cap_sec_cat_name: '',
    access_status: AppStatus.UNLISTED,
    preview_link: '',
    preview_type: PreviewType.iframe,
    access_info: {
      request_auth_link: '',
      request_auth_tip: '',
      request_body: [],
      request_demo: '',
      request_endpoint: '',
      request_headers: [],
      request_method: '',
      request_path: '',
      request_queries: [],
      response_fail: '',
      response_params: [],
    },
  };
  const appInfo = ref<typeof defaultAppInfo>(defaultAppInfo);
  const getAppInfo = async () => {
    const res = await storeAppApi.detail({ id: Number(id) });
    appInfo.value = res.data || defaultAppInfo;
  };

  const popularApps = ref<PopularAppType[]>([]);
  const getPopularApps = async () => {
    const res = await storeAppApi.recommend({ scene: 'HOT' });
    popularApps.value = res.data.list;
  };

  const capBreadcrumb = computed(() => {
    return [
      { label: '能力分类', value: '' },
      {
        label: appInfo.value.cap_pri_cat_name,
        value: appInfo.value.cap_pri_cat_id,
      },
      {
        label: appInfo.value.cap_sec_cat_name,
        value: appInfo.value.cap_sec_cat_id,
      },
    ];
  });
  const handleBreadcrumbClick = (item: string | number, index: number) => {
    if (index === 0) {
      router.push(`/app`);
    } else {
      router.push(`/app?level=${index}&id=${item}`);
    }
  };

  // 应用接入
  const handleAccessApp = async () => {
    router.push(`/account/ak`);
  };

  const handlePreviewApp = async () => {
    if (appInfo.value.preview_type === PreviewType.tab) {
      window.open(appInfo.value.preview_link, '_blank');
      return;
    }
    Modal.open({
      title: '应用预览',
      content: () => (
        <iframe
          src={appInfo.value.preview_link}
          style="width: 100%; border: none; overflow: hidden; margin: 0; min-height: calc(100vh - 200px)"
          allow="microphone"
        />
      ),
      modalStyle: {
        width: '50%',
      },
      footer: false,
      bodyStyle: {
        height: '100%',
      },
    });
  };

  const handlePopularAppClick = (appId: number) => {
    router.push(`/app/detail/${appId}`);
  };

  onMounted(() => {
    getAppInfo();
    getPopularApps();
  });
</script>

<style scoped lang="less">
  .app-detail {
    max-width: 1520px;
    padding: 0 40px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0 auto;

    .content {
      display: flex;
      gap: 35px;
      width: 100%;
      margin-bottom: 90px;

      .content-left {
        width: calc(100% - 35px - 388px);
        flex: 1;
        gap: 24px;
        padding: 24px 35px;
      }

      .content-right {
        max-width: 388px;

        :deep(.arco-card-body) {
          padding: 24px;
        }

        .popular-app {
          display: flex;
          flex-direction: column;
          gap: 10px;
          cursor: pointer;

          :deep(.arco-divider) {
            margin: 13px 0;
          }

          .info {
            display: flex;
            align-items: center;
            gap: 12px;

            span {
              font-size: 18px;
              color: #222;
              font-weight: 500;
            }
          }

          .des {
            font-size: 14px;
            color: #666;
            line-height: 1.7;
          }
        }

        img {
          width: 44px;
          height: 44px;
          border-radius: 50%;
          object-fit: cover;
        }
      }
    }
  }

  .shadow {
    box-shadow: 0 2px 4px 0 rgb(37 48 68 / 4%), 0 6px 12px 0 rgb(37 48 68 / 12%);
    border-radius: 8px;
    background: #fff;
  }

  .card {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .avatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgb(0 0 0 / 3%);
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #222;
      }
    }

    .description {
      margin-top: 15px;
      margin-bottom: 24px;
      font-size: 14px;
      color: #666;
      line-height: 1.7;
      min-height: 40px;
    }

    .card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 88px;
      background: #e7eeff;
      border-radius: 8px;
      padding: 0 32px;
      margin-bottom: 24px;

      .price {
        font-weight: 500;

        .price-text {
          font-size: 28px;
          color: #ff9100;
          margin-right: 4px;
          line-height: 40px;
        }

        .price-unit {
          line-height: 14px;
          font-size: 10px;
          color: rgb(0 0 0 / 84%);
        }
      }

      .detail-btn {
        font-size: 12px;
        color: rgb(11 38 64 / 68%);
      }
    }

    .read-agreement {
      margin: 24px 0;
      height: 20px;
      font-size: 14px;
      color: rgb(0 0 0 / 40%);
      line-height: 20px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .create-btn-group {
      display: flex;
      gap: 12px;

      .create-btn {
        width: 120px;
        height: 40px;
        background: linear-gradient(270deg, #ff9f00 0%, #ff7d00 100%);
        border-radius: 8px;
        font-weight: 500;
        font-size: 16px;
        color: #fff;
      }
    }
  }
</style>
