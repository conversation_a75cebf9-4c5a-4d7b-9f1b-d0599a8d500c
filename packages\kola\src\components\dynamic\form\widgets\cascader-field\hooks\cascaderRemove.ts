import { onBeforeUnmount, onMounted, ref } from 'vue';
import { isFunction } from 'lodash';

function useCascaderRemove({ handleRemove, props, cascaderFormItemRef }) {
  function removeEvent(e) {
    // 注册删除事件
    if (e.target.closest('.arco-tag-close-btn')) {
      handleRemove();
    }
  }

  function getNode() {
    return cascaderFormItemRef?.value?.$el?.querySelector(
      `#${props.field.name} .arco-select-view-inner`
    );
  }

  onMounted(() => {
    if (isFunction(props.field?.onRemove)) {
      const node = getNode();
      node?.addEventListener('click', removeEvent, true);
    }
  });
  onBeforeUnmount(() => {
    if (isFunction(props.field?.onRemove)) {
      const node = getNode();
      node?.removeEventListener('click', removeEvent);
    }
  });
}

export default useCascaderRemove;
