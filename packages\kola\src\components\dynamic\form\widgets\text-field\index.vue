<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="fieldLabel"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :hide-asterisk="field.hideAsterisk"
    :rules="rules"
  >
    <a-input
      v-model="value"
      :disabled="disabled"
      :readonly="field.readonly"
      :placeholder="placeholderString"
      :max-length="field.maxLength"
      :input-attrs="field.inputAttrs"
      :style="field?.style"
      :show-word-limit="field.showWordLimit"
      :allow-clear="field.allowClear"
      @blur="onblur"
      :word-length="field.wordLength"
      :word-slice="field.wordSlice"
      @clear="handleClear"
    >
      <template #prepend v-if="field.preText">
        {{ field.preText }}
      </template>
      <template #prefix v-if="field.prefix">
        {{ field.prefix }}
      </template>
      <template #suffix v-if="field.suffix">
        {{ field.suffix }}
      </template>
    </a-input>
    <template #extra v-if="field.extra">
      <component :is="field.extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import { computed, inject, ref, watchEffect } from 'vue';
  import { isFunction } from 'lodash';
  import { TextField } from '../../../types/form';

  const props = defineProps<{
    field: TextField;
    path: string;
  }>();

  const formData = inject('formData');
  const fieldLabel = computed(() => {
    return isFunction(props.field.label)
      ? props.field.label(props.field, formData?.value as any, props.path)
      : props.field.label;
  });
  const disabled = computed(() => {
    return isFunction(props.field.disabled)
      ? props.field.disabled(props.field, formData?.value as any, props.path)
      : props.field.disabled;
  });
  const rules = computed(() => {
    if (!isFunction(props.field.required)) {
      return props.field.rules;
    }

    const requiredStatus = props.field.required(
      props.field,
      formData?.value as any,
      props.path
    );
    return [
      { required: requiredStatus, message: `${fieldLabel.value}是必填项` },
    ];
  });
  const value = defineModel<string>({
    default: '',
    set(val) {
      props.field.onChange?.(val, formData);
      if (props.field.setter) {
        return props.field.setter(val);
      }
      return val;
    },
  });

  function onblur() {
    props.field.onBlur?.({
      value: value.value,
      formData: formData?.value,
    });
  }
  function handleClear() {
    props.field.onClear?.({
      value: value.value,
      formData: formData?.value,
    });
  }

  const placeholderString = computed(() => {
    const { placeholder } = props.field;
    if (isFunction(placeholder)) {
      return placeholder(props.field, formData?.value as any, props.path);
    }
    return props.field.placeholder || `请输入${fieldLabel.value}`;
  });
</script>
