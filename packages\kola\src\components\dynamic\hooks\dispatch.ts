import { inject } from 'vue';
import emitter from '../emitter';
import { PageEvent } from '../types/event';

const useDispatch = (pageId?: string) => {
  pageId = pageId ?? inject<string>('pageId', '');

  const dispatch = (action: PageEvent) => {
    emitter.emit(
      pageId ? `${pageId}-${action.type}` : action.type,
      action.payload
    );
  };

  return dispatch;
};

export default useDispatch;
