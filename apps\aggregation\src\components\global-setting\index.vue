<template>
  <div class="fixed-settings" @click="setVisible">
    <a-button type="primary">
      <template #icon>
        <icon-settings />
      </template>
    </a-button>
  </div>
  <a-drawer
    :width="300"
    unmount-on-close
    :visible="visible"
    :cancel-text="$t('settings.close')"
    :ok-text="$t('settings.saveSettings')"
    @ok="saveSettings"
    @cancel="cancel"
  >
    <template #title> {{ $t('settings.title') }}</template>
    <Block :options="contentOpts" :title="$t('settings.content')" />
    <Block :options="othersOpts" :title="$t('settings.otherSettings')" />
  </a-drawer>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  // import { useI18n } from 'vue-i18n';
  import { pick } from 'lodash';
  // import { useAppStore } from '@/store';
  import Block from './block.vue';

  const emit = defineEmits(['cancel']);
  const canEditConfigList = [
    'topMenu',
    'tabBar',
    'menuWidth',
    'colorWeak',
    'theme',
  ];

  // const appStore = useAppStore();
  // const { t } = useI18n();
  const visible = computed(() => true);
  const contentOpts = computed(() => [
    {
      name: 'settings.topMenu',
      key: 'topMenu',
      // defaultVal: appStore.topMenu,
    },
    // { name: 'settings.tabBar', key: 'tabBar', defaultVal: appStore.tabBar },
    {
      name: 'settings.menuWidth',
      key: 'menuWidth',
      // defaultVal: appStore.menuWidth,
      type: 'number',
    },
  ]);
  const othersOpts = computed(() => [
    // {
    //   name: 'settings.colorWeak',
    //   key: 'colorWeak',
    //   defaultVal: appStore.colorWeak,
    // },
    {
      name: 'settings.navbar.theme.toDark',
      key: 'themeDark',
      // defaultVal: appStore.theme === 'dark',
    },
  ]);

  const cancel = () => {
    // appStore.updateSettings({ globalSettings: false });
    emit('cancel');
  };
  const saveSettings = async () => {
    // const curSettings = pick(appStore.$state, canEditConfigList);
    const text = JSON.stringify({}, null, 2);

    localStorage.setItem('globalSettings', text);
    // appStore.updateSettings({ globalSettings: false });
  };
  const setVisible = () => {
    // appStore.updateSettings({ globalSettings: true });
  };
</script>

<style scoped lang="less">
  .fixed-settings {
    position: fixed;
    top: 280px;
    right: 0;

    svg {
      font-size: 18px;
      vertical-align: -4px;
    }
  }
</style>
