<template>
  <a-upload
    class="excel-upload"
    accept=".xlsx, .xls"
    :disabled="loading"
    :limit="0"
    :show-file-list="false"
    :custom-request="customRequest"
  >
    <template #upload-button>
      <a-button type="text" :loading="loading">Excel 文件上传</a-button>
    </template>
  </a-upload>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const emit = defineEmits(['parse']);
  const props = defineProps({
    parseExcelRequest: { type: Function, default: () => Promise.resolve([]) },
  });
  const loading = ref(false);

  function customRequest(option) {
    const { onError, onSuccess, fileItem } = option;
    loading.value = true;
    props
      .parseExcelRequest(fileItem.file)
      .then((list) => {
        loading.value = false;
        emit('parse', list);
        onSuccess();
      })
      .catch((err) => {
        loading.value = false;
        onError(err);
      });
  }
</script>

<style lang="less" scoped>
  :deep(.arco-upload-list) {
    display: none;
  }

  :deep(.arco-upload-hide) {
    display: inline-block;
  }
</style>
