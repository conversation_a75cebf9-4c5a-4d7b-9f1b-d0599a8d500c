<template>
  <a-modal
    :visible="!!previewVisible"
    @cancel="handleCancel"
    :footer="false"
    :title="modalTitle"
    title-align="start"
    width="900px"
    :body-style="{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 0,
      height: '600px',
    }"
  >
    <div
      class="previous-btn"
      v-if="isGroup"
      :class="{ disabled: previewIndex === 0 }"
      @click="handleLeft"
    >
      <IconLeft size="40" />
    </div>
    <div class="video-container">
      <img
        :src="currentVideo?.imgSrc"
        v-if="currentVideo?.imgSrc"
        class="preview-img"
        alt=""
      />
      <video
        :src="currentVideo?.videoSrc"
        v-else
        controls
        class="preview-video"
        height="auto"
        width="400px"
      ></video>
    </div>
    <c-indicator
      :count="videos?.length || 0"
      :active-index="previewIndex"
    ></c-indicator>
    <div
      class="next-btn"
      v-if="isGroup"
      :class="{ disabled: previewIndex === (videos ?? []).length - 1 }"
      @click="handleRight"
    >
      <IconRight size="40" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, ref, defineProps } from 'vue';
  import CIndicator from './indicator.vue';

  const props = defineProps<{
    src?: string;
    isGroup: boolean;
    currentIndex?: number;
    videos?: any[];
    previewTitle?: string;
  }>();

  const modalTitle = computed(() => {
    const title = props.previewTitle || '视频素材';
    return `${title}${previewIndex.value + 1}`;
  });

  const previewIndex = ref<number>(props.currentIndex ?? 0);

  const previewVisible = defineModel<boolean>('previewVisible', {});

  const currentVideo = computed(() => {
    return props.videos?.[previewIndex.value];
  });

  const handleCancel = () => {
    previewVisible.value = false;
  };

  const handleLeft = () => {
    if (previewIndex.value === 0) return;
    previewIndex.value -= 1;
  };
  const handleRight = () => {
    if (previewIndex.value === (props.videos ?? []).length - 1) return;
    previewIndex.value += 1;
  };
</script>

<style scoped lang="less">
  .video-container {
    height: 500px;
    overflow: hidden;
    display: flex;
    align-items: center;
  }

  .previous-btn {
    margin-right: 100px;
  }

  .next-btn {
    margin-left: 100px;
  }

  .previous-btn,
  .next-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;

    &:hover {
      background-color: var(--color-neutral-2);
      cursor: pointer;
    }
  }

  .disabled {
    &:hover {
      background-color: unset;
      cursor: not-allowed;
    }
  }

  .video-container {
    width: 500px;
    display: flex;
    justify-content: center;
  }

  .preview-img,
  .preview-video {
    max-width: 100%;
    max-height: calc(100% - 16px);
  }
</style>
